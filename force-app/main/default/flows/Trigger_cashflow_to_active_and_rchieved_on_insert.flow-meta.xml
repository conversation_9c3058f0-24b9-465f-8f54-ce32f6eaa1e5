<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <assignments>
        <name>ids_of_active_cashflows</name>
        <label>ids of active cashflows</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>cashflow</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loop_on_active_cashflows</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>cashflow.Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Archived</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>cashflow.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loop_on_active_cashflows.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>cashflowCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>cashflow</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_on_active_cashflows</targetReference>
        </connector>
    </assignments>
    <environments>Default</environments>
    <interviewLabel>Trigger cashflow to active and rchieved on insert {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Trigger cashflow to active and archieved on insert</label>
    <loops>
        <name>loop_on_active_cashflows</name>
        <label>loop on active cashflows</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>Get_all_cashflows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>ids_of_active_cashflows</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Records_1</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Get all cashflow but with only same project id and status in active</description>
        <name>Get_all_cashflows</name>
        <label>Get all cashflows</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>loop_on_active_cashflows</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Project__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Project__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Cashflow__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Records_1</name>
        <label>Update Records 1</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>update_status_to_active</targetReference>
        </connector>
        <inputReference>cashflowCollection</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>update_status_to_active</name>
        <label>update status to active</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_all_cashflows</targetReference>
        </connector>
        <object>Cashflow__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>cashflow</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Cashflow__c</objectType>
    </variables>
    <variables>
        <name>cashflowCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Cashflow__c</objectType>
    </variables>
</Flow>
