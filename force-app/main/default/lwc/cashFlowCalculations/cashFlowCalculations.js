/* cashflowCalculations.js */

/**
 * Shared calculation utilities for CashflowContainer LWC.
 *
 * Expects:
 *  - cashFlowData0: array of { id, weeks: [ { date, value, payment } ] }
 *  - weekColumns:  array of { date, … }
 *  - opportunityData: array or single record with Closing_Cost__c & Interest_Rate__c
 *  - projectData: array or single record with Remaining_Contract_Value__c & Retainage__c
 */

/** Helpers to pull rates from opportunityData */
export function getRates(opportunityData) {
    if(!opportunityData) return { mfLoanRate: 0, accrualRate: 0 };
  const rec = Array.isArray(opportunityData)
    ? opportunityData[0]
    : opportunityData || {};
  const mfLoanRate  = (Number(rec.Closing_Cost__c) || 0) / 100;
  const accrualRate = (Number(rec.Interest_Rate__c)  || 0) / 100;
  return { mfLoanRate, accrualRate };
}

/**
 * 0) Base loan disbursement + orig-fee series,
 *    ignoring any payments entirely.
 *
 * Returns [{ iso, beg, loanDisb, origFees, outstanding }, …]
 */
export function computeLoanSeriesBase(cashFlowData, opportunityData) {
    if(!opportunityData || !cashFlowData) return [];
  const { mfLoanRate } = getRates(opportunityData);
  const row = cashFlowData.find(r => r.id === 'MFLoanDisb');
  if (!row?.weeks) return [];

  const weeks = [...row.weeks].sort(
    (a, b) => new Date(a.date) - new Date(b.date)
  );
  let prevOut = 0;

  return weeks.map(wk => {
    const loanDisb = Number(wk.value) || 0;
    const origFees = loanDisb / (1 - mfLoanRate) - loanDisb;
    const beg      = prevOut;
    const out      = beg + loanDisb + origFees;
    prevOut = out;

    return { iso: wk.date, beg, loanDisb, origFees, outstanding: out };
  });
}

/**
 * 1) Interest Accrual series (no payments applied):
 *    [{ iso, begInterest, accrual, outstandingInt }, …]
 */
export function computeInterestSeries(loanSeries, opportunityData) {
    if(!opportunityData || !loanSeries) return [];
  const { accrualRate } = getRates(opportunityData);
  let prevIntOut = 0;

  return loanSeries.map((entry, i) => {
    const accrual = i === 0
      ? (entry.beg / (1 - accrualRate)) - entry.beg
      : 7 * (accrualRate / 30) * loanSeries[i - 1].outstanding;

    const begInterest    = prevIntOut;
    const outstandingInt = begInterest + accrual;
    prevIntOut = outstandingInt;

    return { iso: entry.iso, begInterest, accrual, outstandingInt };
  });
}

/**
 * 2) Pay Application series with applied amounts and payments:
 *    [{ iso, gross, pctFrac, nominal, appliedToMfLoan, interestPayment, loanPayment }, …]
 */
export function computePayAppSeries(
  cashFlowData,
  weekColumns,
  loanSeries,
  interestSeries
) {
    if(!cashFlowData || !weekColumns || !loanSeries || !interestSeries) return [];
  const projW = cashFlowData.find(r => r.id === 'ProjectedNetPayApp')?.weeks || [];
  const pctW  = cashFlowData.find(r => r.id === 'payAppAppliedmfloan')?.weeks  || [];

  return weekColumns.map(wc => {
    const iso     = wc.date;
    const gross   = projW.find(w => w.date === iso)?.value  || 0;
    const pctFrac = (pctW.find(w => w.date === iso)?.value || 0) / 100;
    const nominal = gross * pctFrac;

    const loanE = loanSeries.find(e => e.iso === iso)           || { beg: 0, loanDisb: 0, origFees: 0 };
    const intE  = interestSeries.find(e => e.iso === iso)       || { begInterest: 0, accrual: 0 };

    const limit  = loanE.beg
                 + loanE.loanDisb
                 + loanE.origFees
                 + intE.begInterest
                 + intE.accrual;

    const applied         = gross === 0
      ? 0
      : Math.min(nominal, limit);

    const grossInt        = intE.begInterest + intE.accrual;
    const interestPayment = Math.min(grossInt, applied);

    const loanPayment = applied > 0
      ? applied - interestPayment
      : 0;

    return {
      iso,
      gross,
      pctFrac,
      nominal,
      appliedToMfLoan: applied,
      interestPayment,
      loanPayment
    };
  });
}

/**
 * 3) Final Loan Disbursement series with payments applied:
 *    [{ iso, beg, loanDisb, origFees, payment, outstanding }, …]
 */
export function computeLoanSeries(
  cashFlowData,
  weekColumns,
  opportunityData
) {
    if(!cashFlowData || !weekColumns || !opportunityData) return [];
  const base     = computeLoanSeriesBase(cashFlowData, opportunityData);
  const interest = computeInterestSeries(base, opportunityData);
  const payApp   = computePayAppSeries(cashFlowData, weekColumns, base, interest);

  let prevOut = 0;
  return weekColumns.map(wc => {
    const iso   = wc.date;
    const loanE = base.find(e => e.iso === iso)  || { loanDisb: 0, origFees: 0 };
    const payE  = payApp.find(p => p.iso === iso) || { loanPayment: 0 };
    const beg   = prevOut;
    const out   = beg + loanE.loanDisb + loanE.origFees - payE.loanPayment;
    prevOut = out;

    return {
      iso,
      beg,
      loanDisb:    loanE.loanDisb,
      origFees:    loanE.origFees,
      payment:     payE.loanPayment,
      outstanding: out
    };
  });
}

/**
 * 4) Contract Value series:
 *    [{ iso, beg, payApp, ending }, …]
 */
export function computeContractValueSeries(projectData, cashFlowData) {
    if(!projectData || !cashFlowData) return [];
  const proj       = Array.isArray(projectData) ? projectData[0] : projectData || {};
  const remain     = Number(proj.Remaining_Contract_Value__c) || 0;
  const retainFrac = (Number(proj.Retainage__c)             || 0) / 100;
  let prevEnding  = remain * (1 - retainFrac);

  const payW = cashFlowData.find(r => r.id === 'ProjectedNetPayApp')?.weeks || [];
  payW.sort((a, b) => new Date(a.date) - new Date(b.date));

  return payW.map(wk => {
    const gross  = Number(wk.value) || 0;
    const beg    = prevEnding;
    const ending = beg - gross;
    prevEnding   = ending;

    return { iso: wk.date, beg, payApp: gross, ending };
  });
}

/**
 * 5) Outstanding Less Orig Fees:
 *    [{ iso, value }, …]
 */
export function computeOutstandingLessOrigFees(
  cashFlowData,
  opportunityData,
  weekColumns
) {
    if(!cashFlowData || !opportunityData || !weekColumns) return [];
  const loanSeries = computeLoanSeries(cashFlowData, weekColumns, opportunityData);
  return weekColumns.map(wc => {
    const entry = loanSeries.find(e => e.iso === wc.date) || { origFees: 0, outstanding: 0 };
    return { iso: wc.date, value: entry.outstanding - entry.origFees };
  });
}

/**
 * 6) Interest display rows (with payment applied):
 *    [{ iso, begBalance, accrual, payment, outstandingInt }, …]
 */
export function computeInterestDisplayRows(
  cashFlowData,
  weekColumns,
  opportunityData
) {
    if(!cashFlowData || !weekColumns || !opportunityData) return [];
  const base     = computeLoanSeriesBase(cashFlowData, opportunityData);
  const interest = computeInterestSeries(base, opportunityData);
  const payApp   = computePayAppSeries(cashFlowData, weekColumns, base, interest);

  return weekColumns.map(wc => {
    const intE = interest.find(e => e.iso === wc.date)
               || { begInterest: 0, accrual: 0, outstandingInt: 0 };
    const pmt  = payApp.find(p => p.iso === wc.date)?.interestPayment || 0;
    return {
      iso:            wc.date,
      begBalance:     intE.begInterest,
      accrual:        intE.accrual,
      payment:        pmt,
      outstandingInt: intE.outstandingInt - pmt
    };
  });
}