/* eslint-disable */
import { LightningElement, api } from 'lwc';

export default class PayApplication extends LightningElement {
  @api cashFlowData     = [];
  @api weekColumns      = [];
  @api opportunityData  = [];
  @api payappSeries     = [];

  get displayRows() {
    return this.payappSeries.map((entry, idx) => {
      const { iso, gross, pctFrac, appliedToMfLoan } = entry;
      return {
        id:              `${iso}-${idx}`,
        date:            new Date(iso).toLocaleDateString(),
        grossPayApp:     `$${gross.toFixed(2)}`,
        percentToMfLoan: `${(pctFrac * 100).toFixed(2)}%`,
        appliedToMfLoan: `$${appliedToMfLoan.toFixed(2)}`
      };
    });
  }
}