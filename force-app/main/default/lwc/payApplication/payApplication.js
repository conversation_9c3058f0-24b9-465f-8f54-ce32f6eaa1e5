/* eslint-disable */


import { LightningElement, api } from 'lwc';

export default class PayApplication extends LightningElement {
  /** Inputs */
  @api cashFlowData = [];
  @api weekColumns  = [];
  @api opportunityData = [];

  /** 
   * Loan‐disbursement orig‐fee rate as fraction (e.g. 20% → 0.20)
   */
  get mfLoanRate() {
    const rec = Array.isArray(this.opportunityData) && this.opportunityData.length
      ? this.opportunityData[0]
      : this.opportunityData || {};
    return typeof rec.Closing_Cost__c === 'number'
      ? rec.Closing_Cost__c / 100
      : 0;
  }

  /** Interest‐accrual rate as fraction (e.g. 5% → 0.05) */
  get accrualRate() {
    const rec = Array.isArray(this.opportunityData) && this.opportunityData.length
      ? this.opportunityData[0]
      : this.opportunityData || {};
    return typeof rec.Interest_Rate__c === 'number'
      ? rec.Interest_Rate__c / 100
      : 0;
  }

  /**
   * 1) Re-run your LoanDisbursement logic to get
   *    [{ iso, id, beg, loanDisb, origFees, outstanding }, …]
   */
  computeLoanSeries() {
    const row = this.cashFlowData.find(r => r.id === 'MFLoanDisb');
    if (!row?.weeks) {
      return [];
    }
    const sorted = [...row.weeks].sort(
      (a, b) => new Date(a.date) - new Date(b.date)
    );
    let prevOutstanding = 0;
    return sorted.map(wk => {
      const loanDisb = wk.value || 0;
      const origFees = (loanDisb / (1 - this.mfLoanRate)) - loanDisb;
      const payment  = wk.payment || 0;
      const beg      = prevOutstanding;
      const outstanding = beg + loanDisb + origFees - payment;
      prevOutstanding = outstanding;
      return {
        iso:         wk.date,
        id:          wk.id,
        beg,                     // numeric
        loanDisb,                // numeric
        origFees,                // numeric
        outstanding              // numeric
      };
    });
  }

  /**
   * 2) Re-run your InterestAccrual logic to get
   *    [{ iso, begInterest, accrual, outstandingInt }, …]
   */
  computeInterestSeries() {
    const loanSeries = this.computeLoanSeries();
    if (loanSeries.length === 0) {
      return [];
    }
    const rate = this.accrualRate;
    const series = [];
    let prevInterestOut = 0;

    loanSeries.forEach((entry, idx) => {
      const loanBeg = entry.beg;
      let accrual;
      if (idx === 0) {
        // first‐week: (loanBeg/(1−r)) − loanBeg
        accrual = ((loanBeg / (1 - rate))) - loanBeg;
      } else {
        // later: 7*(r/30)*previous loan‐outstanding
        const prevLoanOut = loanSeries[idx - 1].outstanding;
        accrual = 7 * (rate / 30) * prevLoanOut;
      }
      const begInterest    = prevInterestOut;
      const outstandingInt = begInterest + accrual;
      prevInterestOut = outstandingInt;

      series.push({
        iso:             entry.iso,
        begInterest,              // numeric
        accrual,                  // numeric
        outstandingInt            // numeric
      });
    });

    return series;
  }

  

  get displayRows() {
  // grab & clone the source rows
    const projNode = this.cashFlowData.find(r => r.id === 'ProjectedNetPayApp');
    const pctNode  = this.cashFlowData.find(r => r.id === 'payAppAppliedmfloan');
    const projWeeks = Array.isArray(projNode?.weeks) ? [...projNode.weeks] : [];
    const pctWeeks  = Array.isArray(pctNode?.weeks)  ? [...pctNode.weeks]  : [];

    // precompute the two series
    const loanSeries     = this.computeLoanSeries();
    const interestSeries = this.computeInterestSeries();

    return this.weekColumns.map((wc, idx) => {
      const iso = wc.date;

      // pick off gross & pct
      const gross   = (projWeeks.find(w=>w.date===iso)?.value  ) || 0;
      const pctFrac = (pctWeeks .find(w=>w.date===iso)?.value/100) || 0;
      const nominal = gross * pctFrac;

      // pick off loan & interest entries
      const loanE = loanSeries.find(e=>e.iso===iso)     || { beg:0, loanDisb:0, origFees:0 };
      const intE  = interestSeries.find(e=>e.iso===iso) || { begInterest:0, accrual:0 };

      // apply your rule
      let applied;
      if (gross === 0) {
        applied = 0;
      } else {
        console.log('gross: '+gross+', loanE.beg: '+loanE.beg+', loanE.loanDisb:'+loanE.loanDisb, 'loanE.origFees: '+loanE.origFees);
        const limit = 
          loanE.beg +
          loanE.loanDisb +
          loanE.origFees +
          intE.begInterest +
          intE.accrual;
        applied = nominal > limit ? limit : nominal;
      }

      return {
        id:              `${iso}-${idx}`,
        date:            new Date(iso).toLocaleDateString(),
        grossPayApp:     `$ ${gross.toFixed(2)}`,
        percentToMfLoan: `${(pctFrac*100).toFixed(2)}%`,
        appliedToMfLoan: `$ ${applied.toFixed(2)}`
      };
    });
  }

}