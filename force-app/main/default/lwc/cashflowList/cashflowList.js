import { LightningElement, api, wire, track } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { refreshApex } from '@salesforce/apex';
import getCashflowsForProject from '@salesforce/apex/ProjectCashflowController.getCashflowsForProject';
import deleteCashflow from '@salesforce/apex/ProjectCashflowController.deleteCashflow';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
// Import the LightningConfirm module
import LightningConfirm from 'lightning/confirm';

const COLUMNS = [
    { label: 'Cashflow Name', fieldName: 'Name', type: 'text', sortable: true, initialWidth: 250 },
    { label: 'Status', fieldName: 'Status__c', type: 'text', sortable: true },
    { label: 'Version', fieldName: 'Version_Number__c', type: 'number', sortable: true, cellAttributes: { alignment: 'left' } },
    { label: 'Total Value', fieldName: 'Total_Project_Value__c', type: 'currency', sortable: true },
    { label: 'Forecast Start', fieldName: 'Forecast_Start_Date__c', type: 'date-local', sortable: true, typeAttributes:{ month:"2-digit", day:"2-digit", year:"numeric"} },
    { label: 'Project Start', fieldName: 'Project_Start_Date__c', type: 'date-local', sortable: true, typeAttributes:{ month:"2-digit", day:"2-digit", year:"numeric"} },
    { label: 'Created Date', fieldName: 'CreatedDate', type: 'date', sortable: true, typeAttributes:{ year:"numeric", month:"short", day:"2-digit", hour:"2-digit", minute:"2-digit"} },
    {
        type: 'button',
        initialWidth: 120,
        typeAttributes: {
            label: 'Open',
            name: 'open_cashflow',
            title: 'Open this cashflow record',
            variant: 'brand-outline',
            iconName: 'utility:open'
        }
    },
    {
        type: 'button',
        initialWidth: 120,
        typeAttributes: {
            class: 'slds-icon-text-error',
            label: 'Delete',
            name: 'delete_cashflow',
            title: 'Delete Cashflow',
            variant: 'border-filled',
            iconName: 'utility:delete'
        }
    }
];

export default class CashflowList extends NavigationMixin(LightningElement) {
    @api recordId;
    @track cashflows = [];
    @track error;
    @track isLoading = true;
    columns = COLUMNS;
    @track sortBy = 'CreatedDate';
    @track sortDirection = 'desc';

    wiredCashflowsResult;

    @wire(getCashflowsForProject, { projectId: '$recordId' })
    wiredCashflows(result) {
        this.isLoading = true;
        this.wiredCashflowsResult = result;
        if (result.data) {
            this.cashflows = result.data.map(cf => ({...cf}));
            this.error = undefined;
            this.sortData(this.sortBy, this.sortDirection);
            this.isLoading = false;
        } else if (result.error) {
            this.error = result.error;
            this.cashflows = [];
            this.errorMessage = this.reduceErrors(result.error).join(', ');
            this.showToast('Error Loading Cashflows', this.errorMessage, 'error');
            this.isLoading = false;
        }
    }

    handleRowAction(event) {
        const actionName = event.detail.action.name;
        const row = event.detail.row;

        if (actionName === 'open_cashflow') {
            if (row.Id) {
                this[NavigationMixin.Navigate]({
                    type: 'standard__navItemPage',
                    attributes: {
                        apiName: 'CashFlow'
                    },
                    state: {
                        c__recordId: row.Id,
                        c__projectId: this.recordId
                    }
                });
            } else {
                this.showToast('Error', 'Cashflow ID is missing.', 'error');
            }
        } else if (actionName === 'delete_cashflow') { // Updated action name
            this.handleDelete(row.Id);
        }
    }

    // Updated to be async and use LightningConfirm
    async handleDelete(recordId) {
        const confirmResult = await LightningConfirm.open({
            message: 'Are you sure you want to delete this cashflow and all its related data? This action cannot be undone.',
            variant: 'headerless', // Keeps the UI clean
            label: 'Delete Confirmation'
        });

        // Proceed only if the user confirmed
        if (!confirmResult) {
            return;
        }

        this.isLoading = true;
        deleteCashflow({ cashflowId: recordId })
            .then(() => {
                this.showToast('Success', 'Cashflow record was deleted.', 'success');
                return refreshApex(this.wiredCashflowsResult);
            })
            .catch(error => {
                const errorMessage = this.reduceErrors(error).join(', ');
                this.showToast('Error Deleting Record', errorMessage, 'error');
            })
            .finally(() => {
                this.isLoading = false;
            });
    }


    handleSort(event) {
        this.sortBy = event.detail.fieldName;
        this.sortDirection = event.detail.sortDirection;
        this.sortData(this.sortBy, this.sortDirection);
    }

    sortData(fieldName, direction) {
        if (!this.cashflows || this.cashflows.length === 0) return;

        let parseData = JSON.parse(JSON.stringify(this.cashflows));
        let keyValue = (a) => a[fieldName];
        let isReverse = direction === 'asc' ? 1 : -1;

        parseData.sort((x, y) => {
            x = keyValue(x) || '';
            y = keyValue(y) || '';
            return isReverse * ((x > y) - (y > x));
        });
        this.cashflows = parseData;
    }

    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
        });
        this.dispatchEvent(event);
    }

    reduceErrors(errors) {
        if (!Array.isArray(errors)) {
            errors = [errors];
        }
        return (
            errors
                .filter((error) => !!error)
                .map((error) => {
                    if (Array.isArray(error.body)) {
                        return error.body.map((e) => e.message);
                    } else if (error.body && typeof error.body.message === 'string') {
                        return error.body.message;
                    } else if (typeof error.message === 'string') {
                        return error.message;
                    }
                    return error.statusText || JSON.stringify(error);
                })
                .reduce((prev, curr) => prev.concat(curr), [])
                .filter((message) => !!message)
        );
    }

    get errorMessage() {
        return this.error ? this.reduceErrors(this.error).join(', ') : '';
    }
}