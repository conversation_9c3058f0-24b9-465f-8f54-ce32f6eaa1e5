/* eslint-disable */
import { LightningElement, api } from 'lwc';

export default class InterestAccrual extends LightningElement {
  @api cashFlowData    = [];
  @api opportunityData  = [];
  @api weekColumns     = [];

  connectedCallback() {
    console.log('cashFlowData:', JSON.stringify(this.cashFlowData));
    console.log('opportunityData:', JSON.stringify(this.opportunityData));
  }

  /** loan‐fee rate fraction */
  get mfLoanRate() {
    const rec = Array.isArray(this.opportunityData) && this.opportunityData.length
      ? this.opportunityData[0]
      : this.opportunityData || {};
    const raw = rec.Closing_Cost__c;
    const pct = typeof raw === 'number' ? raw : parseFloat(raw) || 0;
    return pct / 100;
  }

  /** accrual interest rate fraction */
  get accrualRate() {
    const rec = Array.isArray(this.opportunityData) && this.opportunityData.length
      ? this.opportunityData[0]
      : this.opportunityData || {};
    const raw = rec.Interest_Rate__c;
    const pct = typeof raw === 'number' ? raw : parseFloat(raw) || 0;
    return pct / 100;
  }

  /** same as your LoanDisbursement series: [{iso,id,beg,loanDisb,origFees,payment,outstanding},…] */
  computeLoanSeries() {
    const row = this.cashFlowData.find(r => r.id === 'MFLoanDisb');
    if (!row?.weeks) return [];
    const weeks = [...row.weeks]; // clone before sort
    weeks.sort((a, b) => new Date(a.date) - new Date(b.date));

    let prevOutstanding = 0;
    return weeks.map(wk => {
      const loanDisb = wk.value  || 0;
      const origFees = loanDisb / (1 - this.mfLoanRate) - loanDisb;
      const payment  = wk.payment || 0;
      const beg      = prevOutstanding;
      const outstanding = beg + loanDisb + origFees - payment;
      prevOutstanding = outstanding;
      return { iso: wk.date, id: wk.id, beg, loanDisb, origFees, payment, outstanding };
    });
  }

  get displayRows() {
    // grab & clone the two payApp‐nodes
    const projNode = this.cashFlowData.find(r => r.id === 'ProjectedNetPayApp');
    const pctNode  = this.cashFlowData.find(r => r.id === 'payAppAppliedmfloan');
    const projWeeks = Array.isArray(projNode?.weeks) ? [...projNode.weeks] : [];
    const pctWeeks  = Array.isArray(pctNode?.weeks)  ? [...pctNode.weeks]  : [];

    // precompute your loan + interest series
    const loanSeries     = this.computeLoanSeries();
    if (loanSeries.length === 0) {
      return [];
    }

    const rate = this.accrualRate;
    const rows = [];
    let prevInterestOut = 0;

    // iterate in weekColumns order to guarantee alignment
    for (let i = 0; i < this.weekColumns.length; i++) {
      const iso = this.weekColumns[i].date; // e.g. "2025-06-06"

      // 1) find the loan‐series entry for this week
      const loanE = loanSeries.find(e => e.iso === iso) || { beg: 0, outstanding: 0 };

      // 2) compute accrual
      let accrual;
      if (i === 0) {
        accrual = (loanE.beg / (1 - rate)) - loanE.beg;
      } else {
        const prevLoanOut = loanSeries[i - 1]?.outstanding || 0;
        accrual = 7 * (rate / 30) * prevLoanOut;
      }

      // 3) beg = previous interest outstanding
      const beg = prevInterestOut;

      // 4) gross interest due this week
      const grossInt = beg + accrual;

      // 5) find gross PayApp & percent for cap
      const gross = projWeeks.find(w => w.date === iso)?.value || 0;
      const pctFrac = (pctWeeks.find(w => w.date === iso)?.value || 0) / 100;
      const appliedToMfLoan = gross * pctFrac;

      // 6) payment = min(grossInt, appliedToMfLoan)
      const payment = grossInt > appliedToMfLoan ? appliedToMfLoan : grossInt;

      // 7) outstandingInt after payment
      const outstandingInt = grossInt - payment;
      prevInterestOut = outstandingInt;

      rows.push({
        id:             `${iso}-${i}`,
        date:           new Date(iso).toLocaleDateString(),
        begBalance:     `$ ${beg.toFixed(2)}`,
        accrual:        `$ ${accrual.toFixed(2)}`,
        payment:        `$ ${payment.toFixed(2)}`,
        outstandingInt: `$ ${outstandingInt.toFixed(2)}`
      });
    }

    return rows;
  }
}