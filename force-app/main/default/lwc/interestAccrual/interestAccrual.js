/* eslint-disable */
import { LightningElement, api } from 'lwc';

export default class InterestAccrual extends LightningElement {
  @api cashFlowData        = [];
  @api opportunityData     = [];
  @api weekColumns         = [];
  @api interestDisplayRow  = [];

  get displayRows() {
    return this.interestDisplayRow.map((entry, idx) => {
      const { iso, begBalance, accrual, payment, outstandingInt } = entry;
      return {
        id:             `${iso}-${idx}`,
        date:           new Date(iso).toLocaleDateString(),
        begBalance:     `$${begBalance.toFixed(2)}`,
        accrual:        `$${accrual.toFixed(2)}`,
        payment:        `$${payment.toFixed(2)}`,
        outstandingInt: `$${outstandingInt.toFixed(2)}`
      };
    });
  }
}