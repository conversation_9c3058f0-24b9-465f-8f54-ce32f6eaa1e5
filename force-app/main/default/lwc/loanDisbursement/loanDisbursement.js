/* eslint-disable */
import { LightningElement, api } from 'lwc';

export default class LoanDisbursement extends LightningElement {
  // @api cashFlowData   = [];
  // @api weekColumns    = [];
  // @api opportunityData= [];
  @api loanSeries     = [];

  /**
   * Build the rows for your template entirely from loanSeries.
   * Automatically numbers the “week” as long as outstanding > 0.
   */
  get displayRows() {
    let weekCount = 0;
    return this.loanSeries.map(entry => {
      const { iso, beg, loanDisb, origFees, payment, outstanding } = entry;
      // bump or reset the week counter
      weekCount = outstanding > 0 ? weekCount + 1 : 0;
      return {
        id:         iso,
        date:       new Date(iso).toLocaleDateString(),
        week:       weekCount,
        beg:        `$${beg.toFixed(2)}`,
        loanDisb:   `$${loanDisb.toFixed(2)}`,
        origFees:   `$${origFees.toFixed(2)}`,
        payment:    `$${payment.toFixed(2)}`,
        outstanding:`$${outstanding.toFixed(2)}`
      };
    });
  }
}