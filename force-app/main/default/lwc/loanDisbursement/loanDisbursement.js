// /* eslint-disable */


// import { LightningElement, api } from 'lwc';

// export default class LoanDisbursement extends LightningElement {
//   @api cashFlowData = [];
//   @api opportunityData = [];

//   connectedCallback() {
//     console.log('API cashFlowData:', JSON.stringify(this.cashFlowData));
//     console.log('opportunityData for cal: ', JSON.stringify(this.opportunityData));
//     //this.dispatchCloneCashFlowData();
//   }


//   get MFLoanDisbRate(){
//     return this.opportunityData.MFLoan_Disb_rate__c;
//   }
  

  
//   get displayRows() {
//     const mfRow = this.cashFlowData.find(r => r.id === 'MFLoanDisb');
//     if (!mfRow || !Array.isArray(mfRow.weeks)) {
//       return [];
//     }

//     // Ensure weeks sorted by date ascending
//     const sortedWeeks = [...mfRow.weeks].sort((a, b) => new Date(a.date) - new Date(b.date));

//     const rows = [];
//     let prevOutstanding = 0;
//     let weekNumber = 0;

//     sortedWeeks.forEach(wk => {
//       const loanDisb = wk.value || 0;
//       const origFees = (loanDisb / (1-(this.MFLoanDisbRate/100))) - loanDisb;
//       const payment = wk.payment || 0;
//       const beg = prevOutstanding;
//       const outstanding = beg + loanDisb + origFees - payment;

//       // Only increment week count if there's an outstanding balance
//       if (outstanding > 0) {
//         weekNumber++;
//       } else {
//         weekNumber = 0;
//       }

//       rows.push({
//         id: wk.id,
//         date: wk.date,
//         week: weekNumber,
//         beg: `$ ${beg.toFixed(2)}`,
//         loanDisb: `$ ${loanDisb.toFixed(2)}`,
//         origFees: `$ ${origFees.toFixed(2)}`,
//         payment: `$ ${payment.toFixed(2)}`,
//         outstanding: `$ ${outstanding.toFixed(2)}`,
//       });

//       prevOutstanding = outstanding;
//     });

//     return rows;
//   }
// }

/* eslint-disable */
import { LightningElement, api } from 'lwc';

export default class LoanDisbursement extends LightningElement {
  @api cashFlowData    = [];
  @api weekColumns     = [];
  @api opportunityData  = [];

  /** loan‐fee rate fraction */
  get mfLoanRate() {
    const rec = Array.isArray(this.opportunityData) && this.opportunityData.length
      ? this.opportunityData[0]
      : this.opportunityData || {};
    const raw = rec.Closing_Cost__c;
    const pct = typeof raw === 'number' ? raw : parseFloat(raw) || 0;
    return pct / 100;
  }

  /** accrual‐interest rate fraction */
  get accrualRate() {
    const rec = Array.isArray(this.opportunityData) && this.opportunityData.length
      ? this.opportunityData[0]
      : this.opportunityData || {};
    const raw = rec.Interest_Rate__c;
    const pct = typeof raw === 'number' ? raw : parseFloat(raw) || 0;
    return pct / 100;
  }

  /**
   * 1) Build the “loan” series:
   *    [{ iso, id, beg, loanDisb, origFees, payment, outstanding }, …]
   */
  computeLoanSeries() {
    const row = this.cashFlowData.find(r => r.id === 'MFLoanDisb');
    if (!row?.weeks) return [];
    const weeks = [...row.weeks].sort((a,b)=>new Date(a.date)-new Date(b.date));
    let prevOut = 0;
    return weeks.map(wk => {
      const loanDisb = wk.value || 0;
      const origFees = loanDisb / (1 - this.mfLoanRate) - loanDisb;
      const payment  = wk.payment || 0;
      const beg      = prevOut;
      const out      = beg + loanDisb + origFees - payment;
      prevOut = out;
      return {
        iso:        wk.date,
        id:         wk.id,
        beg,
        loanDisb,
        origFees,
        payment,
        outstanding: out
      };
    });
  }

  /**
   * 2) Build the “interest” series:
   *    [{ iso, begInterest, accrual, outstandingInt }, …]
   */
  computeInterestSeries(loanSeries) {
    if (loanSeries.length === 0) return [];

    const r = this.accrualRate;
    let prevIntOut = 0;
    return loanSeries.map((entry, i) => {
      let accrual;
      if (i === 0) {
        accrual = (entry.beg / (1 - r)) - entry.beg;
      } else {
        const prevLoanOut = loanSeries[i-1].outstanding;
        accrual = 7 * (r/30) * prevLoanOut;
      }
      const begInterest    = prevIntOut;
      const outstandingInt = begInterest + accrual;
      prevIntOut = outstandingInt;
      return {
        iso:             entry.iso,
        begInterest,
        accrual,
        outstandingInt
      };
    });
  }

  /**
   * 3) Build the PayApp series:
   *    [{ iso, gross, pctFrac, nominal, appliedToMfLoan }, …]
   */
  computePayAppSeries() {
    const projNode = this.cashFlowData.find(r => r.id === 'ProjectedNetPayApp');
    const pctNode  = this.cashFlowData.find(r => r.id === 'payAppAppliedmfloan');
    const projW = Array.isArray(projNode?.weeks) ? [...projNode.weeks] : [];
    const pctW  = Array.isArray(pctNode?.weeks)  ? [...pctNode.weeks]  : [];

    return this.weekColumns.map(wc => {
      const iso      = wc.date;
      const gross    = projW.find(w=>w.date===iso)?.value || 0;
      const pctFrac  = (pctW.find(w=>w.date===iso)?.value || 0) / 100;
      const nominal  = gross * pctFrac;
      return { iso, gross, pctFrac, nominal };
    });
  }

  /**
   * 4) Now tie them all together and compute loan‐disb payment:
   */
  // get displayRows() {
  //   const loanSeries     = this.computeLoanSeries();
  //   const interestSeries = this.computeInterestSeries(loanSeries);
  //   const payAppSeries   = this.computePayAppSeries();

  //   let weekCount = 0;
  //   let prevOut   = 0;

  //   return this.weekColumns.map((wc, idx) => {
  //     const iso = wc.date;

  //     // loan entry for this week
  //     const loanE = loanSeries.find(e=>e.iso===iso) || { beg:0, loanDisb:0, origFees:0 };

  //     // payApp entry
  //     const payE  = payAppSeries[idx] || { nominal:0 };

  //     // interest entry
  //     const intE  = interestSeries.find(e=>e.iso===iso) || { outstandingInt:0 };

  //     // compute payment = IF(applied>0, applied − interestPayment, 0)
  //     // here interestPayment = intE.outstandingInt − intE.begInterest
  //     const interestPayment = intE.outstandingInt - intE.begInterest;
  //     const payment = payE.nominal > 0
  //       ? (payE.nominal - interestPayment)
  //       : 0;

  //     // now origFees & outstanding
  //     const loanDisb = loanE.loanDisb;
  //     const origFees = loanE.origFees;
  //     const beg      = prevOut;
  //     const out      = beg + loanDisb + origFees - payment;

  //     if (out > 0) {
  //       weekCount++;
  //     } else {
  //       weekCount = 0;
  //     }

  //     prevOut = out;

  //     return {
  //       id:          `${iso}-${idx}`,
  //       date:        new Date(iso).toLocaleDateString(),
  //       week:        weekCount,
  //       beg:         `$ ${beg.toFixed(2)}`,
  //       loanDisb:    `$ ${loanDisb.toFixed(2)}`,
  //       origFees:    `$ ${origFees.toFixed(2)}`,
  //       payment:     `$ ${payment.toFixed(2)}`,
  //       outstanding: `$ ${out.toFixed(2)}`
  //     };
  //   });
  // }

  get displayRows() {
    const loanSeries     = this.computeLoanSeries();
    const interestSeries = this.computeInterestSeries(loanSeries);

    let weekCount = 0;
    let prevOut   = 0;

    return this.weekColumns.map((wc, idx) => {
      const iso = wc.date;

      // 1) loan entry
      const loanE = loanSeries.find(e=>e.iso===iso) || { beg:0, loanDisb:0, origFees:0 };

      // 2) interest entry
      const intE  = interestSeries.find(e=>e.iso===iso) || { begInterest:0, accrual:0, outstandingInt:0 };

      // 3) pull actual PayApp-applied from your cashFlowData nodes
      const projNode = this.cashFlowData.find(r => r.id === 'ProjectedNetPayApp');
      const pctNode  = this.cashFlowData.find(r => r.id === 'payAppAppliedmfloan');
      const gross    = projNode?.weeks.find(w=>w.date===iso)?.value || 0;
      const pctFrac  = (pctNode?.weeks.find(w=>w.date===iso)?.value || 0)/100;
      const rawNom   = gross * pctFrac;

      // 4) cap at loan+fees+interest limit
      const limit    = loanE.beg + loanE.loanDisb + loanE.origFees + intE.begInterest + intE.accrual;
      const applied  = rawNom > limit ? limit : rawNom;

      // 5) interest payment this week
      const interestPayment = intE.outstandingInt - intE.begInterest;

      // 6) loan‐disbursement payment = applied >0 ? applied − interestPayment : 0
      const payment = applied > 0 ? (applied - interestPayment) : 0;

      // 7) recompute outstanding
      const beg      = prevOut;
      const out      = beg + loanE.loanDisb + loanE.origFees - payment;
      prevOut = out;

      // 8) week counter
      if (out > 0) weekCount++;
      else          weekCount = 0;

      return {
        id:          `${iso}-${idx}`,
        date:        new Date(iso).toLocaleDateString(),
        week:        weekCount,
        beg:         `$ ${beg.toFixed(2)}`,
        loanDisb:    `$ ${loanE.loanDisb.toFixed(2)}`,
        origFees:    `$ ${loanE.origFees.toFixed(2)}`,
        payment:     `$ ${payment.toFixed(2)}`,
        outstanding: `$ ${out.toFixed(2)}`
      };
    });
  }
}