/* Keep existing styles from cashflowProjectCashflow.css */
/* Sticky first column and header */
.cashflow-table th.slds-cell-fixed,
.cashflow-table td.slds-cell-fixed {
    position: sticky;
    left: 0;
    z-index: 1;
    /* Cells below header */
    background-color: white;
    /* Default for data cells */
    border-right: 1px solid #dddbda;
    /* SLDS var(--slds-g-color-border-base-4, #dddbda) */
}

.cashflow-table thead th.slds-cell-fixed {
    /* Pinned header cells */
    z-index: 3 !important;
    /* Above everything */
    background-color: #f3f2f2;
    /* SLDS var(--slds-g-color-neutral-base-95, #f3f2f2) */
}

.cashflow-table thead th.header-top-left {
    border-bottom: 1px solid #c9c7c5;
    /* Match other header bottom borders */
}




/* Table header styling */
.cashflow-table thead th {
    background-color: #f3f2f2;
    /* SLDS var(--slds-g-color-neutral-base-95, #f3f2f2) */
    border-bottom: 1px solid #c9c7c5;
    /* SLDS var(--slds-g-color-border-base-2, #c9c7c5) */
    vertical-align: middle;
    position: sticky;
    top: 0;
    /* First header row sticks to top */
    z-index: 2;
    /* Header cells above body */
    white-space: nowrap;
    /* Prevent wrapping in headers */
    padding-top: var(--slds-spacing-x-small);
    padding-bottom: var(--slds-spacing-x-small);
    font-weight: var(--slds-g-font-weight-bold, 700);
}

.cashflow-table thead tr.header-row-1 th {
    /* Styles for the first header row (Project Week X) */
    /* border-bottom: none; /* Remove if only one border is desired between header rows */
    font-size: var(--slds-g-font-size-2, 0.75rem);
    /* Figma: 12px */
    color: #080707;
    /* SLDS var(--slds-g-color-neutral-base-10, #080707) */
}

.header-main-title {
    font-size: var(--slds-g-font-size-3, 0.8125rem);
    /* Slightly larger for main title */
    font-weight: var(--slds-g-font-weight-bold, 700);
    margin-left: 5px;
}


.cashflow-table thead tr.header-row-2 th {
    /* Styles for the second header row (Week Ending Date) */
    top: 2.1rem;
    /* Approximate height of the first header row + border; adjust as needed */
    border-top: 1px solid #dddbda;
    /* SLDS var(--slds-g-color-border-base-4, #dddbda) */
    font-size: var(--slds-g-font-size-2, 0.75rem);
    /* Figma: 12px */
    color: #3e3e3c;
    /* SLDS var(--slds-g-color-neutral-base-30, #3e3e3c) */
    font-weight: var(--slds-g-font-weight-regular, 400);
}

.header-sub-title {
    font-size: var(--slds-g-font-size-2, 0.75rem);
    color: #514f4d;
    /* SLDS var(--slds-g-color-neutral-base-40, #514f4d) */
    margin-left: 5px;
}

.week-header-cell,
.date-header-cell {
    min-width: 120px;
    /* Ensure week columns have a minimum width */
    max-width: 170px;
}


/* Section header styling from Figma */
.section-header-row th.section-title-cell {
    background-color: #f3f2f2;
    /* Light grey, similar to table headers */
    font-weight: var(--slds-g-font-weight-bold, 700);
    border-top: 1px solid #c9c7c5;
    border-bottom: 1px solid #c9c7c5;
    padding: var(--slds-spacing-xx-small) var(--slds-spacing-small);
    text-align: left;
    font-size: var(--slds-g-font-size-3, 0.8125rem);
    /* Figma: 10px bold, adjust to SLDS scale */
    color: #080707;
    position: sticky;
    top: 4.2rem;
    z-index: 2; 
}


.section-title-wrapper {
    width: 100%;
}

.section-label {
    text-transform: uppercase;
    /* As seen in Figma for section titles */
}

/* Style for Add button in section header */
.add-button {
    --sds-c-button-text-color: var(--slds-g-color-neutral-base-100, #ffffff);
    --sds-c-button-text-color-hover: var(--slds-g-color-neutral-base-100, #ffffff);
    --sds-c-button-color-background: #44A569;
    /* Green from Figma */
    --sds-c-button-color-border: #44A569;
    --sds-c-button-color-background-hover: #3a8e59;
    /* Darker green */
    --sds-c-button-color-border-hover: #3a8e59;
    font-size: var(--slds-g-font-size-1, 0.625rem);
    /* Figma "Add" button text: 8px */
    /* height: 20px; */
    /* Adjust if needed for small button */
    /* line-height: 20px; */
    padding-left: var(--slds-spacing-x-small);
    padding-right: var(--slds-spacing-x-small);
    min-height: 22px;
}

.add-button .slds-button__icon {
    width: 0.75rem;
    height: 0.75rem;
}


/* Calculated total column styling */
.calculated-total {
    font-weight: var(--slds-g-font-weight-bold, 700);
    background-color: #f9f9f9;
    /* Light grey for total cells */
}

.cashflow-table th.total-header {
    font-weight: var(--slds-g-font-weight-bold, 700);
    background-color: #f3f2f2;
    position: sticky;
    right: 0;
    /* Stick to the right if you have a horizontal scroll */
    z-index: 2;
    /* Above data cells */
    border-left: 1px solid #c9c7c5;
    text-align: center;
}

.cashflow-table td.calculated-total {
    position: sticky;
    right: 0;
    z-index: 1;
    border-left: 1px solid #dddbda;
    background-color: #f9f9f9;
    /* Lighter than header */
    text-align: right;
    /* Numbers usually right-aligned */
}

/* Table layout */
.cashflow-table {
    table-layout: fixed;
    width: 100%;
    border: 1px solid #dddbda;
    border-collapse: separate;
    /* Important for sticky */
}

.cashflow-table td,
.cashflow-table th {
    white-space: nowrap;
    padding: var(--slds-spacing-vertical-xx-small) var(--slds-spacing-horizontal-small);
    /* Reduced padding for denser table */
    height: var(--slds-size-x-small, 2rem);
    /* Reduced height */
    vertical-align: middle;
    font-size: var(--slds-g-font-size-2, 0.75rem);
    /* Figma: 10px for data cells */
}

.cashflow-table td {
    border-top: 1px solid #dddbda;
}

.cashflow-table th[scope="row"] {
    /* Row labels */
    text-align: left;
}




.calculated-row {
    font-weight: var(--slds-g-font-weight-bold, 700);
}

.summary-row {
    background-color: #f3f4f6;
    font-weight: var(--slds-g-font-weight-bold, 700);
}

.summary-row td,
.summary-row th {
    border-top: 2px solid #c9c7c5;
}

/* Thicker top border for summary rows */

/* Specific text styling from Figma */
.text-green {
    color: #2CAC68 !important;
    /* Green from Figma: rgba(44, 172, 104, 1) */
}

/* Styling for icons next to row labels */
.category-icon {
    fill: #514f4d;
    /* Default icon color */
}


/* Cell Button for editable cells */
.cell-button {
    text-align: center;
    width: 100%;
    height: 100%;
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;
    font-size: inherit;
    font-family: inherit;
    color: inherit;
    line-height: inherit;
    display: block;
}

.cell-button .cell-value-display {
    /* Target the lightning-formatted-number inside button */
    pointer-events: none;
    display: block;
    width: 100%;
    padding: var(--slds-spacing-vertical-xx-small) var(--slds-spacing-horizontal-small);
}

.cell-button:hover,
.cell-button:focus {
    background-color: #f3f2f2;
    text-decoration: none;
    outline: 1px solid #005fb2;
    /* SLDS focus blue */
}

.cell-clickable {
    padding: 0 !important;
    /* Remove padding from td so button can fill it */
}

/* Highlight difference cells as per Figma */
.wholelightblue {
    background: #cfe2f3 !important;
    /* Light blue from Figma */
    background-color: #cfe2f3 !important;
    /* Light blue from Figma */
}

.cell-difference .cell-value-display {
    background: #cfe2f3 !important;
    /* Light blue from Figma */
    background-color: #cfe2f3 !important;
    /* Light blue from Figma */
}

.cell-clickable.cell-difference .cell-button:hover .cell-value-display {
    background: #b0d1f1 !important;
    /* Darker shade on hover */
    background-color: #b0d1f1 !important;
    /* Darker shade on hover */
}

.cell-clickable.cell-difference .cell-button:hover {
    background: #b0d1f1 !important;
    /* Darker shade on hover */
    background-color: #b0d1f1 !important;
    /* Darker shade on hover */
}

/* Conditional cell backgrounds for totals */
.cell-positive-background {
    background-color: #a8e6cf !important;
    /* Lighter green (adjust to match Figma's green) */
    background: #a8e6cf !important;
    /* Lighter green (adjust to match Figma's green) */
    /* color: #155724; */
}

.cell-clickable.cell-positive-background .cell-button:hover .cell-value-display {
    background-color: #87d8b5 !important;
    background: #87d8b5 !important;
}

.cell-negative-background {
    background-color: #ffab91 !important;
    /* Lighter red (adjust to match Figma's red) */
    /* color: #721c24; */
}

.cell-clickable.cell-negative-background .cell-button:hover .cell-value-display {
    background-color: #ff8a65 !important;
}

/* Ensure scrollable container */
.slds-scrollable_x {
    overflow-x: auto;
    overflow-y: visible;
}

/* Specific row label styling */
.row-label-bold {
    font-weight: var(--slds-g-font-weight-bold, 700);
}

.row-label-semibold {
    font-weight: var(--slds-g-font-weight-semibold, 600);
    /* if available, else bold */
}

/* Align text to right for numerical data cells by default */
.cashflow-table td:not(.slds-cell-fixed) {
    /* Target data cells, not the row label th */
    text-align: cetner;
}

/* Row labels (first column) should be left-aligned */
.cashflow-table th.slds-cell-fixed {
    text-align: left;
}


/* cashflowProjectCashflowTab.css contents integrated here if needed */
.table-controls-bar {
    padding-top: var(--slds-spacing-small);
    padding-bottom: var(--slds-spacing-small);
    background-color: #f9f9f9;
    /* Light background for control bar */
    border-bottom: 1px solid #dddbda;
}

.table-control-dropdown {
    /* max-width: 200px; */
    display: inline-block;
    margin-right: var(--slds-spacing-medium);
}

.cell-updated .cell-value-display {
    background-color: #fff8c4 !important;
}

.cell-updated {
    background-color: #fff8c4 !important;
}

.custom-green-button {
    background-color: #44A569;
    /* Bootstrap-like green */
    border: 1px solid #44A569;
    color: #fff;
    padding: 0.5rem 1rem;
    padding-left: 20px;
    height: 32px;
    width: 70px;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease, border-color 0.2s ease;
}

/* 1) Limit the horizontal “window” to 250px + (12 × 120px) = 1690px */
.table-outer-wrapper {
    width: 100%;
    margin: 0 auto;
}
.table-scroll-container {
    overflow-x: auto;        
    /* max-width = pinned column + 12 columns */
    /* max-width: 1690px;        */
    /* hide vertical scrollbar if undesired */
    overflow-y: hidden;      
}

/* 2) Pin the leftmost “PROJECT WEEK” column */
.sticky-col {
    position: sticky;
    left: 0;
    background: #fff;     /* match your table’s background */
    z-index: 2;           /* sit on top of other cells */
}

/* 3) Ensure header’s sticky cells are above the pinned left cells */
.header-row-1 .sticky-col,
.header-row-2 .sticky-col {
    z-index: 3;
}

/* 4) Force each “week” column (header + cell) to be exactly 120px wide */
.week-header-cell,
.date-header-cell,
.cashflow-table td {
    width: 120px;
    min-width: 120px;
}

/* 5) The pinned left column is fixed at 250px */
/* (we set that inline in the template, but you can also add here if you prefer) */
.header-fixed-col.sticky-col,
.cashflow-table th.sticky-col {
    padding-left: 10px;
    width: 250px;
    min-width: 250px;
}


/* total column */
.total-column {
    min-width: 120px;
    width: 120px;     
    text-align: center;
    font-weight: 700;
    background-color: #f9f9f9;
    transition: width 0.3s ease-in-out, min-width 0.3s ease-in-out, padding 0.3s ease-in-out, opacity 0.3s linear;
    border-right: 1px solid #dddbda; 
    overflow: hidden;
    white-space: nowrap;
    opacity: 1;
}

.total-column.is-hidden {
    min-width: 0;
    width: 0;
    padding-left: 0;
    padding-right: 0;
    border-right: none;
    opacity: 0;
}

.cashflow-table thead th.total-column {
    background-color: #f3f2f2;
}

.column-toggler {
    border: 1px solid #c9c7c5;
    border-radius: 4px;
    background-color: white;
}