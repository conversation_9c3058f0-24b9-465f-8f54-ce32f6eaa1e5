/* eslint-disable */
import { LightningElement, api, track, wire } from 'lwc';
import getCashflowData from '@salesforce/apex/CashflowDataService.getCashflowData';
import MFL_LOGO from '@salesforce/resourceUrl/mfLogoLwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CurrentPageReference } from 'lightning/navigation';

// ——————————————————————————————————————————————————————————————————————————————
// If you want to pull “all expense categories” from a picklist, re-import these:
import CASHFLOW_LINE_ITEM_OBJECT from '@salesforce/schema/Cashflow_Line_Item__c';
import { getObjectInfo, getPicklistValues } from 'lightning/uiObjectInfoApi';
import CATEGORY_FIELD from '@salesforce/schema/Cashflow_Line_Item__c.Line_Item_Category__c';
// ——————————————————————————————————————————————————————————————————————————————

const CALCULATED_ROW_TYPES = ['DATA_CALCULATED_TOTAL', 'DATA_CALCULATED_SUMMARY'];
const CHILD_LINE_ITEMS_REL_NAME = 'Cashflow_Line_Item_Details__r';
const POPOVER_ROW_TYPES = [
    'DATA_EXPENSE',
    'DATA_FINANCING_EDITABLE',
    'DATA_REVENUE_EDITABLE',
    'DATA_READONLY'
];

const createSectionHeader = (label, showAdd = false) => {
    var id = `section-${label.replace(/\s+/g, '').toLowerCase()}`;
    return {
        id,
        label,
        isSectionHeader: true,
        sectionId: id,
        showAddButton: showAdd,
        type: 'SECTION_HEADER',
        level: 0,
        weeks: [],
        calculatedTotal: null,
        rowClass: 'slds-text-title_caps section-header'
    };
};

export default class LoanSummaryClientView extends LightningElement {
    // ────────────────────────────────────────────────────────────────────────────
    // 1) Reactive / tracked properties
    // ────────────────────────────────────────────────────────────────────────────
    @api recordId;
    @track isLoading = true;
    @track error;

    // Header data
    logoUrl = MFL_LOGO;
    @track customerName = '';
    @track approvedFunding = '';
    @track projectName = '';
    // projectGross is now a getter, see section 2

    // Raw data containers
    @track weekColumns = [];
    @track cashFlowData = [];
    @track disbursementData = [];
    @track payApplicationData = [];
    @track transactions = [];

    // If you need “all expense categories” from the picklist:
    @track expenseCategoryOptions = [];
    defaultRecordTypeId;
    @track loanDate;
    @track matureDate;
    // Internal (non-@track)
    _projectData = null;
    _activeCashflow = null;
    _forecastLines = [];
    _currentProjectId = null;
    _currentCashflowId = null;
    _urlCashflowId = null;

    @wire(CurrentPageReference)
    pageRef;

    getLabelForCategory(apiName) {
    var option = this.expenseCategoryOptions.find(opt => opt.value === apiName);
    return option ? option.label : apiName; // fallback to API name if label not found
}

    get projectGross() {
        var payAppRow = this.cashFlowData.find(r => r.id === 'PayAppSubmitted');
        if (!payAppRow) {
            return '$ 0.00';
        }
        var total = payAppRow.weeks.reduce((sum, cell) => sum + (parseFloat(cell.value) || 0), 0);
        return `$ ${total.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }

    // 2.2) The “PROJECT COSTS” rows (header + every cost row)
    get projectCostRows() {
        var allRows = this.cashFlowData || [];
        var startIndex = allRows.findIndex(
            r => r.isSectionHeader && r.sectionId === 'section-projectcosts'
        );
        if (startIndex < 0) return [];

        var headerRow = { ...allRows[startIndex] };
        var costRows = [];
        for (let i = startIndex + 1; i < allRows.length; i++) {
            if (allRows[i].isSectionHeader) break;
            costRows.push({ ...allRows[i] });
        }
        return [headerRow, ...costRows];
    }

    // 2.3) halfTotalCols → used for colspan in the table’s section header
    get totalColumnCountInternalPlusTotal() {
        var totalCols = (this.weekColumns?.length || 0) + 1;
        return totalCols;
    }
    get halfTotalCols() {
        var total = this.totalColumnCountInternalPlusTotal || 0;
        return Math.floor(total / 2);
    }

    // 2.4) “MF Disbursement” weeks (for the bottom table)
    get mfDisbursmentWeeks() {
        var row = this.cashFlowData.find(r => r.id === 'MFLoanDisb');
        return row ? row.weeks : [];
    }

    // 2.5) “PayApp” weeks (for the middle bottom table)
    get PayAppWeeks() {
        var row = this.cashFlowData.find(r => r.id === 'ProjectedNetPayApp');
        return row ? row.weeks : [];
    }

    // 2.6) Totals for those two bottom tables
    get mfLoanDisbursementTotal() {
        return this.mfDisbursmentWeeks
            .reduce((sum, cell) => sum + (parseFloat(cell.value) || 0), 0)
            .toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }
    get payAppTotal() {
        return this.PayAppWeeks
            .reduce((sum, cell) => sum + (parseFloat(cell.value) || 0), 0)
            .toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }

    get totalProjectCost() {
        var row = this.cashFlowData.find(r => r.id === 'TotalProjectCost');
        return row ? row.calculatedTotal : 0;
    }

    get estimatedGross(){
        var numericPayAppTotal = parseFloat(this.payAppTotal.replace(/,/g, ''));
        var rounded = Math.round(numericPayAppTotal / 100) * 100;
        var a = Number(rounded);
        var b = Number(this.totalProjectCost);
        if (!isFinite(a) || !isFinite(b) || a === 0) {
            return '0';
        }

        var raw = (a - b) / a;
        return parseFloat(raw.toFixed(2)); 

    }

    get originationFee() {
        if (!this._currentProjectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => {
                return sum +
                    (tx.Default_Fee_Application__c || 0) +
                    (tx.Doc_Stamp_Fees_Application__c || 0) +
                    (tx.Late_Fee_Application__c || 0) +
                    (tx.Legal_Fees_Application__c || 0);
            }, 0);
    }

    get interestIncome() {
        if (!this.projectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => sum + (tx.Interest_Application__c || 0), 0);
    }

    get projectedTotalFinancingExpense(){
        return this.originationFee + this.interestIncome;
    }

    get totalLessRetainage() {
        var row = this.cashFlowData.find(r => r.id === 'LessRetainage');
        if (!row) {
            return 0;
        }
        var rawTotal = row.calculatedTotal;  
        console.log('rawtotal '+rawTotal);         
        var roundedHundreds = Math.round(rawTotal / 100) * 100;
        console.log('roundedHundreds '+roundedHundreds);
        return roundedHundreds;                           
    }
        get borrowerPreFinancingGrossProfit(){
    var numericPayAppTotal = parseFloat(this.payAppTotal.replace(/,/g, ''));
        var rounded = Math.round(numericPayAppTotal / 100) * 100;
        var a = Number(rounded);
        var b = Number(this.totalProjectCost);
        console.log('a'+a);
        console.log('b'+b);
        if (!isFinite(a) || !isFinite(b) || a === 0) {
            return '0';
        }

        var raw = (a - b);
        console.log('raw '+raw);
        var rawdata = raw - this.totalLessRetainage;
        return rawdata; 
        }

        get projectedPostFinancingGrossProfit(){
            return this.borrowerPreFinancingGrossProfit - this.projectedTotalFinancingExpense;
        }

        get projectedAdjustedGrossMargin(){
            var numerator   = Number(this.projectedPostFinancingGrossProfit);
            var denominator = Number(this.projectGross);

            // If either value is invalid or denominator is zero, bail out
            if (!isFinite(numerator) || !isFinite(denominator) || denominator === 0) {
                return 0;
            }

            // Otherwise return the raw ratio
            return numerator / denominator;

        }
    // ────────────────────────────────────────────────────────────────────────────
    // 3) Wire up picklist values for expenseCategoryOptions
    // ────────────────────────────────────────────────────────────────────────────

    @wire(getObjectInfo, { objectApiName: CASHFLOW_LINE_ITEM_OBJECT })
    handleObjectInfo({ data, error }) {
        if (data) {
            this.defaultRecordTypeId = data.defaultRecordTypeId;
        }
    }

    @wire(getPicklistValues, {
        recordTypeId: '$defaultRecordTypeId',
        fieldApiName: CATEGORY_FIELD
    })
    handleCategoryPicklist({ data, error }) {
        if (data) {
            // data.values is an array of { label, value, … }
            this.expenseCategoryOptions = data.values;
            this.renderedCallback();
        }
    }

    // ────────────────────────────────────────────────────────────────────────────
    // 4) Lifecycle & data loading
    // ────────────────────────────────────────────────────────────────────────────

    renderedCallback() {
        if (this.pageRef && !this._currentProjectId) {
            var state = this.pageRef.state;
            this._urlCashflowId = state?.c__recordId;
            this._currentProjectId = state?.c__projectId || this.recordId;

            if (this._currentProjectId) {
                this.loadCashflowDetails(this._currentProjectId, this._urlCashflowId);
            } else {
                this.error = 'Project Record ID is not available.';
                this.isLoading = false;
                this._showToast('Warning', this.error, 'warning');
            }
        }
    }

    async loadCashflowDetails(projectIdToLoad, cashflowIdFromUrl) {
        this.isLoading = true;
        this.error = undefined;

        try {
            var pageData = await getCashflowData({
                projectId: projectIdToLoad,
                cashflowId: cashflowIdFromUrl
            });

            if (!pageData) {
                throw new Error('No data returned from Apex.');
            }

            // — Store raw references
            this._projectData = pageData.project;
            this._activeCashflow = pageData.activeCashflow;
            this._forecastLines = pageData.forecastLines || [];
            console.log('cashflowLineItem ' + JSON.stringify(this._forecastLines));
            this._currentCashflowId = this._activeCashflow?.Id || null;
            this.transactions = pageData.transactions || [];

            // — Populate header fields
            this.loanDate = this._activeCashflow?.Forecast_Start_Date__c || 'N/A';
            this.matureDate = this._activeCashflow?.Forecast_End_Date__c || 'N/A';
            this.customerName = pageData.account.Name;
            this.approvedFunding = this._projectData?.Loan_Principal__c || '0';
            this.projectName = this._projectData?.Name || 'N/A';
            
            // — Build weekColumns (using Forecast_Start_Date__c or earliest Week_Start_Date__c)
            this.weekColumns = this.generateWeekColumnsFromForecast(
                this._forecastLines,
                parseInt(this._activeCashflow?.Projected_Weeks_Outstanding__c || '52', 10)
            );

            // — Convert raw data → cashFlowData (grid of rows + cells)
            this.cashFlowData = this.transformDataToCashflowRows(
                this._projectData,
                this._activeCashflow,
                this._forecastLines,
                this.weekColumns
            );

            // — Related lists (for bottom tables)
            this.disbursementData = pageData.disbursements || [];
            this.payApplicationData = pageData.payApplications || [];

            // — Recalculate all totals so every row’s “calculatedTotal” is correct
            this.recalculateAllTotals();
        } catch (err) {
            console.error('[LoanSummaryClientView] Error loading cashflow data:', err);
            this.error =
                'Failed to load cashflow data: ' +
                (err.body?.message || err.message || 'Unknown');
            this._showToast('Error', this.error, 'error');
        } finally {
            this.isLoading = false;
        }
    }

    // ────────────────────────────────────────────────────────────────────────────
    // 5) Data‐transformation & utility methods
    // ────────────────────────────────────────────────────────────────────────────

    generateWeekColumnsFromForecast(forecastLines, numberOfWeeksToDisplay = 52) {
        let overallStartDate;
        var uniqueDates = new Set();

        forecastLines.forEach(line => {
            if (line.Week_Start_Date__c) {
                uniqueDates.add(line.Week_Start_Date__c);
            }
        });

        if (uniqueDates.size > 0) {
            var sortedDates = Array.from(uniqueDates).sort(
                (a, b) => new Date(a + 'T00:00:00Z') - new Date(b + 'T00:00:00Z')
            );
            overallStartDate = new Date(sortedDates[0] + 'T00:00:00Z');
        } else if (this._activeCashflow?.Forecast_Start_Date__c) {
            overallStartDate = new Date(this._activeCashflow.Forecast_Start_Date__c + 'T00:00:00Z');
        } else {
            return this.createDefaultWeekColumns(numberOfWeeksToDisplay);
        }

        var columns = [];
        for (let i = 0; i < numberOfWeeksToDisplay; i++) {
            var currentDate = new Date(overallStartDate.valueOf());
            currentDate.setUTCDate(currentDate.getUTCDate() + i * 7);

            var dateStr = currentDate.toISOString().split('T')[0];
            columns.push({
                id: `week${i}-${dateStr}`,
                label: `${currentDate.getUTCMonth() + 1}/${currentDate.getUTCDate()}`,
                date: dateStr,
                formattedDate: `${currentDate.getUTCMonth() + 1}/${currentDate.getUTCDate()}/${String(
                    currentDate.getUTCFullYear()
                ).slice(-2)}`
            });
        }
        return columns;
    }

    createDefaultWeekColumns(numberOfWeeks) {
        let columns = [];
        let startDate = new Date();
        var localDay = startDate.getDay();
        var diff = startDate.getDate() - localDay + (localDay === 0 ? -6 : 1);
        startDate = new Date(startDate.setDate(diff));
        startDate.setUTCHours(0, 0, 0, 0);

        for (let i = 0; i < numberOfWeeks; i++) {
            var weekStartDate = new Date(startDate.valueOf());
            weekStartDate.setUTCDate(startDate.getUTCDate() + i * 7);

            var dateStr = weekStartDate.toISOString().split('T')[0];
            columns.push({
                id: `week${i}-default-${dateStr}`,
                label: `${weekStartDate.getUTCMonth() + 1}/${weekStartDate.getUTCDate()}`,
                date: dateStr,
                formattedDate: `${weekStartDate.getUTCMonth() + 1}/${weekStartDate.getUTCDate()}/${String(
                    weekStartDate.getUTCFullYear()
                ).slice(-2)}`
            });
        }
        return columns;
    }

    transformDataToCashflowRows(project, activeCashflow, forecastLines, weekColumns) {
        console.log('[CashflowContainer] transformDataToCashflowRows starting (parent/child model)...');
        var newRows = [];
        // forecastMapByCatAndWeek will store the parent Cashflow_Line_Item__c object,
        // which is assumed to already contain its children in a property like 'Cashflow_Line_Item_Details__r'.
        var forecastMapByCatAndWeek = new Map();
        console.log('forecast lines '+JSON.stringify(forecastLines));
        (forecastLines || []).forEach(parentLine => { // parentLine is a Cashflow_Line_Item__c
            if ((parentLine.Financing_Source__c || parentLine.Line_Item_Category__c) && parentLine.Week_Start_Date__c) {
                var dateKey = parentLine.Week_Start_Date__c;
                var mapKey= '';
                if(parentLine.Line_Item_Category__c == '' || parentLine.Line_Item_Category__c == null) {
                    mapKey = `${parentLine.Type__c}_${parentLine.Financing_Source__c}_${dateKey}`;
                } else {
                    mapKey = `${parentLine.Type__c}_${parentLine.Line_Item_Category__c}_${dateKey}`;
                }
                // Assuming one unique parent Cashflow_Line_Item__c per category and week_start_date from backend
                forecastMapByCatAndWeek.set(mapKey, parentLine);
            }
        });
        console.log('forecastMapByCatAndWeek', JSON.stringify(forecastMapByCatAndWeek));
        //debugger;

        var createEmptyWeeksForDataRow = (rowId, rowLabel, rowType) => {
            return (weekColumns || []).map((col) => {
                var hasPopover = POPOVER_ROW_TYPES.includes(rowType);
                return {
                    id: `cell-${rowId}-${col.id}`, weekIdentifier: col.id, value: 0,
                    weekLabel: col.label, rowLabel: rowLabel,
                    cellClass: 'slds-text-align_center' + (hasPopover ? ' cell-clickable' : ''),
                    hasPopover: hasPopover, date: col.date,
                    salesforceIds: [], // Will hold parent Cashflow_Line_Item__c.Id
                    originalLineItems: [], // Will hold child Cashflow_Line_Item_Detail__c records
                    category: rowType === 'DATA_EXPENSE' ? rowLabel : null, // Default category
                    expenseCategory: rowType === 'DATA_EXPENSE' ? rowLabel : null,
                    variableFixed: 'Fixed', paymentFrequency: 'Weekly', weeklyAmount: 0, paymentTerm: 'Net 30',
                    isDifference: rowType !== 'DATA_CALCULATED_SUMMARY' && rowType !== 'DATA_CALCULATED_TOTAL' && rowType !== 'DATA_READONLY'
                };
            });
        };

        var populateWeeksForRow = (weeksArray, categoryForForecast, rowType, lineItemType) => {
            weeksArray.forEach(weekCell => {
                var forecastKey = `${lineItemType}_${categoryForForecast}_${weekCell.date}`;
                var parentForecastEntry = forecastMapByCatAndWeek.get(forecastKey); // This is the parent Cashflow_Line_Item__c

                if (parentForecastEntry) {
                    weekCell.value = parseFloat(parentForecastEntry.Planned_Amount__c || 0); // Aggregate value from parent
                    weekCell.salesforceIds = [parentForecastEntry.Id]; // Store parent's SF ID
                    // Use the assumed relationship name for children. Ensure it's correct.
                    weekCell.originalLineItems = parentForecastEntry[CHILD_LINE_ITEMS_REL_NAME] ? JSON.parse(JSON.stringify(parentForecastEntry[CHILD_LINE_ITEMS_REL_NAME])) : [];

                    weekCell.weeklyAmount = weekCell.value; // Cell's display amount is the aggregate
                    weekCell.category = parentForecastEntry.Line_Item_Category__c;
                    weekCell.expenseCategory = parentForecastEntry.Line_Item_Category__c;
                    weekCell.type = parentForecastEntry.Type__c;

                    // Default popover fields can come from parent as a template, or first child if children exist
                    if (weekCell.originalLineItems.length > 0) {
                        var repChildItem = weekCell.originalLineItems[0]; // A representative child
                        // Assuming child items might have these specific fields, otherwise use parent's as fallback
                        weekCell.variableFixed = repChildItem.Sub_Amount_Variation__c || parentForecastEntry.Sub_Amount_Variation__c || 'Fixed';
                        weekCell.paymentFrequency = repChildItem.Sub_Payment_Frequency__c || parentForecastEntry.Sub_Payment_Frequency__c || 'Weekly';
                        weekCell.paymentTerm = repChildItem.Sub_Payment_Terms__c || parentForecastEntry.Sub_Payment_Terms__c || 'Net 30';
                    } else {
                        weekCell.variableFixed = parentForecastEntry.Sub_Amount_Variation__c || 'Fixed';
                        weekCell.paymentFrequency = parentForecastEntry.Sub_Payment_Frequency__c || 'Weekly';
                        weekCell.paymentTerm = parentForecastEntry.Sub_Payment_Terms__c || 'Net 30';
                    }
                }
                weekCell.isDifference = rowType !== 'DATA_CALCULATED_SUMMARY' && rowType !== 'DATA_CALCULATED_TOTAL' && rowType !== 'DATA_READONLY';
                if (weekCell.isDifference && POPOVER_ROW_TYPES.includes(rowType)) {
                    weekCell.cellClass = (weekCell.cellClass || '').replace('cell-clickable', '').replace('cell-difference wholelightblue', '').trim();
                    if (POPOVER_ROW_TYPES.includes(rowType)) weekCell.cellClass += ' cell-clickable';
                    weekCell.cellClass += ' cell-difference wholelightblue';
                }
            });
        };

        var createActualDataRow = (rowId, label, type, level, categoryForForecast, isDifferenceOverride, lineItemType) => {
            let weeks = createEmptyWeeksForDataRow(rowId, label, type);
            if (categoryForForecast) {
                populateWeeksForRow(weeks, categoryForForecast, type, lineItemType);
            }
            var isDiffRow = typeof isDifferenceOverride === 'boolean' ? isDifferenceOverride :
                (type !== 'DATA_CALCULATED_SUMMARY' && type !== 'DATA_CALCULATED_TOTAL' && type !== 'DATA_READONLY');

            weeks.forEach(w => {
                w.isDifference = isDiffRow;
                let currentCellClass = w.cellClass || 'slds-text-align_center';
                currentCellClass = currentCellClass.replace('cell-clickable', '').replace('cell-difference wholelightblue', '').trim();
                if (POPOVER_ROW_TYPES.includes(type)) currentCellClass += ' cell-clickable';
                if (isDiffRow && POPOVER_ROW_TYPES.includes(type)) currentCellClass += ' cell-difference wholelightblue';
                w.cellClass = currentCellClass;
            });
            return {
                id: rowId, label: label, isSectionHeader: false, isEditable: POPOVER_ROW_TYPES.includes(type),
                type: type, level: level, parentId: null, weeks: weeks,
                calculatedTotal: weeks.reduce((sum, cell) => sum + (cell.value || 0), 0),
                rowClass: `data-row ${CALCULATED_ROW_TYPES.includes(type) ? 'calculated-row' : ''} ${type === 'DATA_CALCULATED_SUMMARY' ? 'summary-row' : ''}`,
                indentStyle: `padding-left: ${level * 1.5}rem;`
            };
        };

        // --- Define categories & Row Creation (same structure as provided, but data source within cells changes) ---

        var revenueLineItemCategories = new Set();
        var expenseCategoriesForRows = new Set();
        var financeSourcesCategoriesForRows = new Set();

        var financingLineItemCategoriesFromConfig = new Set([
            activeCashflow?.MFLoanDisbursementCategory__c, activeCashflow?.MFLoanRepaymentCategory__c,
            activeCashflow?.OtherSSVCategory__c, activeCashflow?.AlternativeLoanCategory__c
        ].filter(Boolean));

        (forecastLines || []).forEach(line => {
            if ( (line.Line_Item_Category__c || line.Financing_Source__c)) {
                if (line.Type__c === 'Project Revenue') {
                    revenueLineItemCategories.add(line.Line_Item_Category__c);
                } else if (line.Type__c === 'Project Cost') {
                    expenseCategoriesForRows.add(line.Line_Item_Category__c);
                } else if (line.Type__c === 'Financing Source') {
                    financeSourcesCategoriesForRows.add(line.Financing_Source__c);
                }
            }
        });


        newRows.push(createSectionHeader('PROJECT REVENUE'));
        // debugger;
        if (revenueLineItemCategories.size > 0) {
            Array.from(revenueLineItemCategories).sort().forEach(category => {
                console.log('category', category);
                var rowId = `revenue-${category.replace(/[^a-zA-Z0-9]/g, '') || Math.random().toString(36).substr(2, 5)}`;
                console.log('rowId', rowId);
                newRows.push(createActualDataRow(rowId, category, 'DATA_REVENUE_EDITABLE', 1, category, true, 'Project Revenue', null));
            });

        } else {
            // If no specific revenue items are found in the forecast, add default placeholders
            newRows.push(createActualDataRow('payApp', 'Pay App to be Submitted', 'DATA_REVENUE_EDITABLE', 1, 'Invoice Submission', true, 'Project Revenue'));
            newRows.push(createActualDataRow('retainage', 'Less Retainage', 'DATA_REVENUE_EDITABLE', 1, 'Retainage', true, 'Project Revenue'));
        }
        newRows.push(createActualDataRow('ProjectedNetPayApp', 'Projected Net Pay App', 'DATA_CALCULATED_TOTAL', 1, false, 'Project Revenue'));
        newRows.push(createSectionHeader('PROJECT COSTS', true));
        //debugger;
        console.log('forecast lines', forecastLines);
        console.log('revenueLineItemCategories', revenueLineItemCategories);
        console.log('financingLineItemCategoriesFromConfig', financingLineItemCategoriesFromConfig);

        var distinctForecastCategories = new Set((forecastLines || []).map(line => line.Line_Item_Category__c).filter(Boolean));

        distinctForecastCategories.forEach(cat => {
            if (!revenueLineItemCategories.has(cat) && !financingLineItemCategoriesFromConfig.has(cat)) {
                expenseCategoriesForRows.add(cat);
            }
        });
        // expenseCategoriesForRows.clear(); // Clear any old categories

        // (this.expenseCategoryOptions || []).forEach(option => {
        //     var category = option.value;
        //     // Skip if already used for financing or revenue
        //     if (!revenueLineItemCategories.has(category) && !financingLineItemCategoriesFromConfig.has(category)) {
        //         expenseCategoriesForRows.add(category);
        //     }
        // });
        console.log('[CashflowContainer] Identified Expense Categories for dynamic rows:', Array.from(expenseCategoriesForRows));

        
        if (expenseCategoriesForRows.size > 0) {
            Array.from(expenseCategoriesForRows).sort().forEach(category => {
                var rowId = `expense-${category.replace(/[^a-zA-Z0-9]/g, '') || Math.random().toString(36).substr(2, 5)}`;
                newRows.push(createActualDataRow(rowId, category, 'DATA_EXPENSE', 1, category, true, 'Project Cost'));
            });
        } else {
            console.log('[CashflowContainer] No dynamic expense categories found from forecast. Adding placeholder.');
            newRows.push(createActualDataRow('ExpenseCat1Default', 'Expense Category 1 (Example)', 'DATA_EXPENSE', 1, null, true, 'Project Cost'));
        }
        newRows.push(createActualDataRow('TotalProjectCost', 'Total Project Cost', 'DATA_CALCULATED_TOTAL', 1, null, false, 'Project Cost'));

        newRows.push(createSectionHeader('FINANCING SOURCES'));
        // if (expenseCategoriesForRows.size > 0) {
        //     Array.from(expenseCategoriesForRows).sort().forEach((category, idx) => {
        //         // idx is 0,1,2… so add 1
        //         var rowId = `expense-${idx + 1}`;
        //         newRows.push(
        //             createActualDataRow(
        //                 rowId,
        //                 category,
        //                 'DATA_EXPENSE',
        //                 1,
        //                 category,
        //                 true,
        //                 'Financing Source'
        //             )
        //         );
        //     });
        // } else {
        //     console.log('[CashflowContainer] No dynamic expense categories found from forecast. Adding placeholder.');
        //     newRows.push(createActualDataRow('ExpenseCat1Default', 'Expense Category 1 (Example)', 'DATA_EXPENSE', 1, null, true, 'Financing Source'));
        // }
        // newRows.push(createActualDataRow('TotalProjectCost1', 'Total Project Cost', 'DATA_CALCULATED_TOTAL', 1, null, false, 'Financing Source'));
        newRows.push(createActualDataRow('MFLoanDisb', 'MF Loan Disbursement', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.MFLoanDisbursementCategory__c || 'Disbursement', true, 'Financing Source'));
        newRows.push(createActualDataRow('ReceiptPayApp', 'Receipt of Pay App', 'DATA_FINANCING_EDITABLE', 1, 'Pay Application', true, 'Financing Source'));
        newRows.push(createActualDataRow('TotalSources', 'Total Sources of Cash', 'DATA_CALCULATED_TOTAL', 1, null, false, 'Financing Source'));

        newRows.push(createSectionHeader('USES'));
        newRows.push(createActualDataRow('MFLoanRepay', 'MF Loan Repayment', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.MFLoanRepaymentCategory__c || 'MF Loan Repayment', true));
        newRows.push(createActualDataRow('ProjectCostsPaid', 'Project Costs Paid That Week', 'DATA_FINANCING_EDITABLE', 1, null, true));
        newRows.push(createActualDataRow('otherSSV', 'OTHER SSV PAYMENTS REQ\'d', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.OtherSSVCategory__c || 'OTHER SSV PAYMENTS REQ\'d', true));
        newRows.push(createActualDataRow('alternativeLoanPayment', 'Alternative Loan Payment', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.AlternativeLoanCategory__c || 'Alternative Loan Payment', true));
        newRows.push(createActualDataRow('TotalUses', 'Total Uses of Cash', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('payAppAppliedmfloan', '% of Pay APP Applied to MF Loan', 'DATA_FINANCING_EDITABLE', 1, activeCashflow?.AlternativeLoanCategory__c || 'Alternative Loan Payment', true));
        newRows.push(createSectionHeader('CASH FLOW SUMMARY'));
        newRows.push(createActualDataRow('NetWeeklyCashFlow', 'Net Weekly Cash Flow', 'DATA_CALCULATED_SUMMARY', 1, null, false));
        newRows.push(createActualDataRow('AccumulatedSurplus', 'Accumulated Surplus/(Deficit)', 'DATA_CALCULATED_SUMMARY', 1, null, false));
        newRows.push(createSectionHeader('LOAN SCHEDULE'));
        newRows.push(createActualDataRow('NetWeeklyCashflowSurplus', 'Net Weekly Cashflow- SURPLUS/(DEFICIT)', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('AutomatedWeeklyCashflowSurplus', 'Accumulated Weekly Cashflow- SURPLUS/(DEFICIT)', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('BeginningLoanBalance', 'Beginning Loan Balance', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('LoanDisb', 'Loan Disbursements', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('MFOriginationFee', 'MF Origination Fee', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('MFAccruedInterest', 'MF Accrued Interest', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('PaymentAppliedToMFLoan', 'Payment Applied to MF Loan', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('EndingLoanBalance', 'Ending Loan Balance', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('LoanToValue', 'Loan to Value', 'DATA_CALCULATED_TOTAL', 1, null, false));
        newRows.push(createActualDataRow('ContractPoInvoiceValue', 'Contract/P.O./Invoice value', 'DATA_CALCULATED_TOTAL', 1, null, false));
        // newRows.push(createActualDataRow('LoanToValue', 'Loan to Value', DATA_CALCULATED_TOTAL,1,null, false));
        // newRows.push(createActualDataRow('ContractPoInvoiceValue', 'Contract/P.O./Invoice value', DATA_CALCULATED_TOTAL,1, null, false));
        

        // --- START: Special Calculation for MF Loan Disbursement Row ---
        var mfLoanDisbursementRow = newRows.find(row => row.id === 'MFLoanDisb');

        if (mfLoanDisbursementRow) {
            console.log('[transformDataToCashflowRows] Found MF Loan Disbursement row. Applying special conditional calculation...');

            var categoryForForecast = activeCashflow?.MFLoanDisbursementCategory__c || 'Disbursement';

            // Iterate through each week/cell of this specific row
            mfLoanDisbursementRow.weeks.forEach(cell => {
                var forecastKey = `Financing Source_${categoryForForecast}_${cell.date}`;
                var parentForecastEntry = forecastMapByCatAndWeek.get(forecastKey);

                let finalCellValue = 0; // Default to 0

                if (parentForecastEntry) {
                    var parentPlannedAmount = 0;
                    if(parentForecastEntry.Manually_Overwritten__c == true || parentForecastEntry.Value_added_by_new_version_process__c == true) {
                        parentPlannedAmount = parentForecastEntry.Planned_Amount__c != undefined ? parseFloat(parentForecastEntry.Planned_Amount__c) : undefined;    
                    }

                    if (parentPlannedAmount) {
                        finalCellValue = parentPlannedAmount;
                    } else {
                        // --- START: MODIFICATION ---
                        // Correctly access the related disbursement records
                        if (parentForecastEntry.Cashflow_Weekly_Line_Disbursements__r && parentForecastEntry.Cashflow_Weekly_Line_Disbursements__r) {
                            var disbursementJunctions = parentForecastEntry.Cashflow_Weekly_Line_Disbursements__r;
                            
                            finalCellValue = disbursementJunctions.reduce((sum, junction) => {
                                // Check if the nested Disbursement__r record exists
                                if (junction.Disbursement__r) {
                                    var amountApproved = parseFloat(junction.Disbursement__r.Amount_Approved__c || 0);
                                    return sum + amountApproved;
                                }
                                return sum;
                            }, 0);
                        }
                        // --- END: MODIFICATION ---
                    }
                }

                // Overwrite the cell's value with the result of our conditional logic
                cell.value = finalCellValue;
                cell.weeklyAmount = finalCellValue;
            });

            // After updating all cell values, recalculate the total for the entire row
            mfLoanDisbursementRow.calculatedTotal = mfLoanDisbursementRow.weeks.reduce((sum, cell) => sum + (cell.value || 0), 0);
            console.log('[transformDataToCashflowRows] Finished MF Loan Disbursement calculation. New Total:', mfLoanDisbursementRow.calculatedTotal);
        }
        // --- END: Special Calculation ---

         // --- START: Special Calculation for Reciept of Pay app Row ---
        var RecieptPayAppRow = newRows.find(row => row.id === 'ReceiptPayApp');

        if (RecieptPayAppRow) {
            console.log('[transformDataToCashflowRows] Found Reciept of Pay app row. Applying special conditional calculation...');

            var categoryForForecast = 'Pay Application';

            // Iterate through each week/cell of this specific row
            RecieptPayAppRow.weeks.forEach(cell => {
                var forecastKey = `Financing Source_${categoryForForecast}_${cell.date}`;
                var parentForecastEntry = forecastMapByCatAndWeek.get(forecastKey);

                let finalCellValue = 0; // Default to 0

                if (parentForecastEntry) {
                    var parentPlannedAmount = parentForecastEntry.Planned_Amount__c != undefined ? parseFloat(parentForecastEntry.Planned_Amount__c) : undefined;

                    if (parentPlannedAmount) {
                        finalCellValue = parentPlannedAmount;
                    } else {
                        // --- START: MODIFICATION ---
                        // Correctly access the related disbursement records
                        if (parentForecastEntry.Cashflow_Weekly_Line_Pay_Applications__r && parentForecastEntry.Cashflow_Weekly_Line_Pay_Applications__r) {
                            var payApplicationJunctions = parentForecastEntry.Cashflow_Weekly_Line_Pay_Applications__r;
                            
                            finalCellValue = payApplicationJunctions.reduce((sum, junction) => {
                                // Check if the nested Disbursement__r record exists
                                if (junction.Pay_Application__r) {
                                    var amountApproved = parseFloat(junction.Pay_Application__r.Projected_payment__c || 0);
                                    return sum + amountApproved;
                                }
                                return sum;
                            }, 0);
                        }
                        // --- END: MODIFICATION ---
                    }
                }

                // Overwrite the cell's value with the result of our conditional logic
                cell.value = finalCellValue;
                cell.weeklyAmount = finalCellValue;
            });

            // After updating all cell values, recalculate the total for the entire row
            mfLoanDisbursementRow.calculatedTotal = mfLoanDisbursementRow.weeks.reduce((sum, cell) => sum + (cell.value || 0), 0);
            console.log('[transformDataToCashflowRows] Finished MF Loan Disbursement calculation. New Total:', mfLoanDisbursementRow.calculatedTotal);
        }
        // --- END: Special Calculation ---
        var getRow = id => newRows.find(r => r.id === id);
        var nwcs = getRow('NetWeeklyCashflowSurplus');
        var awcs = getRow('AutomatedWeeklyCashflowSurplus');
        var bR = getRow('BeginningLoanBalance');
        var dR = getRow('MFLoanDisb');
        var fR = getRow('MFOriginationFee');
        var iR = getRow('MFAccruedInterest');
        var pR = getRow('PaymentAppliedToMFLoan');
        var eR = getRow('EndingLoanBalance');

        bR.weeks.forEach((cell, idx) => { cell.value = idx === 0 ? 0 : eR.weeks[idx - 1].value; cell.weeklyAmount = cell.value; });
        bR.calculatedTotal = bR.weeks.reduce((sum, c) => sum + c.value, 0);
        eR.weeks.forEach((cell, idx) => {
            var begin = bR.weeks[idx].value;
            var disb = dR.weeks[idx].value;
            var fee = fR.weeks[idx].value;
            var int = iR.weeks[idx].value;
            var pay = pR.weeks[idx].value;
            cell.value = begin + disb + fee + int + pay;
            cell.weeklyAmount = cell.value;
        });
        eR.calculatedTotal = eR.weeks.reduce((sum, c) => sum + c.value, 0);
        console.log('[CashflowContainer] transformDataToCashflowRows finished.');
        return newRows;

        console.log('newRows ' + JSON.stringify(newRows));
    }

    recalculateAllTotals() { 
        if (!this.cashFlowData || this.cashFlowData.length === 0 || !this.weekColumns || this.weekColumns.length === 0) {
            console.warn('[CashflowContainer] Recalculate: cashFlowData or weekColumns are empty.');
            return;
        }
        let updatedData = JSON.parse(JSON.stringify(this.cashFlowData));
        var rowMap = updatedData.reduce((map, row) => { map[row.id] = row; return map; }, {});
        var safeNum = (val) => parseFloat(val) || 0;
        var safeFixed = (val, digits = 2) => safeNum(val).toFixed(digits);

        var sec1HeaderIdx = updatedData.findIndex(r =>
            r.isSectionHeader && r.label === 'PROJECT COSTS'
        );
        var sec1SummaryIdx = updatedData.findIndex(r => r.id === 'TotalProjectCost');
        var section1ExpenseIds = updatedData
            .slice(sec1HeaderIdx + 1, sec1SummaryIdx)
            .filter(r => r.type === 'DATA_EXPENSE')
            .map(r => r.id);

        // 1b) FINANCING SOURCES block
        var sec2HeaderIdx = updatedData.findIndex(r =>
            r.isSectionHeader && r.label === 'FINANCING SOURCES'
        );
        var sec2SummaryIdx = updatedData.findIndex(r => r.id === 'TotalProjectCost1');
        var section2ExpenseIds = updatedData
            .slice(sec2HeaderIdx + 1, sec2SummaryIdx)
            .filter(r => r.type === 'DATA_EXPENSE')
            .map(r => r.id);

        this.weekColumns.forEach((col, weekIdx) => {
            var getWeekVal = (rowId) => safeNum(rowMap[rowId]?.weeks[weekIdx]?.value);

            // if (rowMap.ProjectedNetPayApp) rowMap.ProjectedNetPayApp.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('payApp') + getWeekVal('retainage')));
            
            var getWeekValByLabel = (label) => {
                var row = updatedData.find(r => r.label === label);
                return row?.weeks?.[weekIdx]?.value || 0;
            };
            // console.log('Pay App to be Submitted' + getWeekValByLabel('Pay App to be Submitted'));
            // console.log('Less retainage' + getWeekValByLabel('Less Retainage'));
            if (rowMap.ProjectedNetPayApp) {
                rowMap.ProjectedNetPayApp.weeks[weekIdx].value = parseFloat(
                    safeFixed(getWeekValByLabel('Pay Application to be Submitted') + getWeekValByLabel('Less Retainage'))
                );
            }
            var expenseSum = updatedData.filter(r => r.type === 'DATA_EXPENSE').reduce((sum, r) => sum + safeNum(r.weeks[weekIdx]?.value), 0);
            // if (rowMap.TotalProjectCost) rowMap.TotalProjectCost.weeks[weekIdx].value = parseFloat(safeFixed(expenseSum));
            if (rowMap.TotalProjectCost) {
                var sum1 = section1ExpenseIds.reduce((sum, id) => sum + getWeekVal(id), 0);
                var cell1 = rowMap.TotalProjectCost.weeks[weekIdx];
                cell1.value = parseFloat(safeFixed(sum1));
                cell1.weeklyAmount = cell1.value;
            }
            if (rowMap.ProjectCostsPaid && !rowMap.ProjectCostsPaid.weeks[weekIdx].isManuallySet) {
                rowMap.ProjectCostsPaid.weeks[weekIdx].value = parseFloat(safeFixed(-expenseSum));
            }
            // if (rowMap.ReceiptPayApp) rowMap.ReceiptPayApp.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('ProjectedNetPayApp')));
            if (rowMap.LoanDisb) rowMap.LoanDisb.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('MFLoanDisb')));
            // if (rowMap.TotalSources) rowMap.TotalSources.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('MFLoanDisb') + getWeekVal('ReceiptPayApp')));
            // if (rowMap.TotalSources) rowMap.TotalSources.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('ReceiptPayApp')));
            if (rowMap.TotalSources) {
                var totalSourcesValue = getWeekVal('MFLoanDisb') + getWeekVal('ReceiptPayApp');
                rowMap.TotalSources.weeks[weekIdx].value = parseFloat(safeFixed(totalSourcesValue));
            }
            let projectCostsPaidMagnitude = Math.abs(getWeekVal('ProjectCostsPaid'));
            let totalPositiveUses = getWeekVal('MFLoanRepay') + projectCostsPaidMagnitude + getWeekVal('otherSSV') + getWeekVal('alternativeLoanPayment');
            if (rowMap.TotalUses) rowMap.TotalUses.weeks[weekIdx].value = parseFloat(safeFixed(-totalPositiveUses));
            if (rowMap.NetWeeklyCashFlow) rowMap.NetWeeklyCashFlow.weeks[weekIdx].value = parseFloat(safeFixed(getWeekVal('TotalSources') + getWeekVal('TotalUses')));
            if (rowMap.TotalProjectCost1) {
                var sum2 = section2ExpenseIds.reduce((sum, id) => sum + getWeekVal(id), 0);
                var cell2 = rowMap.TotalProjectCost1.weeks[weekIdx];
                cell2.value = parseFloat(safeFixed(sum2));
                cell2.weeklyAmount = cell2.value;
            }
            if (rowMap.NetWeeklyCashflowSurplus) {
                var val = getWeekVal('TotalSources') + getWeekVal('TotalUses');
                var weekly = parseFloat(safeFixed(val));
                var cell = rowMap.NetWeeklyCashflowSurplus.weeks[weekIdx];
                cell.value = weekly;
                cell.weeklyAmount = weekly;
            }
            if (rowMap.BeginningLoanBalance) { rowMap.BeginningLoanBalance.weeks[weekIdx].value = weekIdx === 0 ? 0 : safeNum(rowMap.EndingLoanBalance.weeks[weekIdx - 1].value); }
            if (rowMap.BeginningLoanBalance) {
                var cell = rowMap.BeginningLoanBalance.weeks[weekIdx];
                if (!cell.isManuallySet) {
                    var startingBalance = safeNum(this._activeCashflow?.Beginning_Loan_Balance__c);
                    var val = weekIdx === 0
                        ? startingBalance
                        : safeNum(rowMap.EndingLoanBalance.weeks[weekIdx - 1].value);
                    cell.value = parseFloat(safeFixed(val));
                    cell.weeklyAmount = parseFloat(safeFixed(val));
                }
            }
            // 2) Ending Loan Balance = begin + disbursements + fee + interest – payment
            if (rowMap.EndingLoanBalance) {
                var begin = safeNum(rowMap.BeginningLoanBalance.weeks[weekIdx].value);
                var disb = getWeekVal('MFLoanDisb');
                var fee = getWeekVal('MFOriginationFee');
                var interest = getWeekVal('MFAccruedInterest');
                var payment = getWeekVal('PaymentAppliedToMFLoan');
                var endVal = begin + disb + fee + interest + payment;
                rowMap.EndingLoanBalance.weeks[weekIdx].value = parseFloat(safeFixed(endVal));
            }

            if (rowMap.ContractPoInvoiceValue) {
                var cell = rowMap.ContractPoInvoiceValue.weeks[weekIdx];
                var contractVal = safeNum(this._activeCashflow?.Contract_PO_Invoice_Value__c);
                cell.value = parseFloat(safeFixed(contractVal));
                cell.weeklyAmount = cell.value;
            }

            // 2) Loan to Value = EndingLoanBalance / ContractPoInvoiceValue  (or 0 if contractVal is 0)
            if (rowMap.LoanToValue) {
                var cell = rowMap.LoanToValue.weeks[weekIdx];
                var ending = safeNum(getWeekVal('EndingLoanBalance'));
                var contract = safeNum(getWeekVal('ContractPoInvoiceValue'));
                console.log('ending / contract' + ending / contract);
                var ltv = contract ? ending / contract : 0;
                cell.value = parseFloat(safeFixed(ltv));
                cell.weeklyAmount = cell.value;
            }
        });

        if (rowMap.AutomatedWeeklyCashflowSurplus && rowMap.NetWeeklyCashflowSurplus) {
            let running = 0;
            this.weekColumns.forEach((col, weekIdx) => {
                var netVal = safeNum(rowMap.NetWeeklyCashflowSurplus.weeks[weekIdx].value);
                running += netVal;
                var accCell = rowMap.AutomatedWeeklyCashflowSurplus.weeks[weekIdx];
                accCell.value = parseFloat(safeFixed(running));
                accCell.weeklyAmount = accCell.value;
            });
        }

        let accumulatedSurplusVal = 0;
        this.weekColumns.forEach((col, weekIdx) => {
            var netWeeklyFlow = safeNum(rowMap.NetWeeklyCashFlow?.weeks[weekIdx]?.value);
            accumulatedSurplusVal += netWeeklyFlow;
            if (rowMap.AccumulatedSurplus) rowMap.AccumulatedSurplus.weeks[weekIdx].value = parseFloat(safeFixed(accumulatedSurplusVal));
        });

        updatedData.forEach(row => {
            if (!row.isSectionHeader) {
                row.calculatedTotal = parseFloat(safeFixed(row.weeks.reduce((sum, cell) => sum + safeNum(cell.value), 0)));
                if (row.id === 'AccumulatedSurplus' && row.weeks.length > 0) {
                    row.calculatedTotal = parseFloat(safeFixed(row.weeks[row.weeks.length - 1].value));
                }
                row.weeks.forEach(cell => {
                    let cellClasses = cell.computedClass || cell.cellClass || 'slds-text-align_center';
                    cellClasses = cellClasses.replace(' cell-positive-background', '').replace(' cell-negative-background', '').trim();
                    if (row.type === 'DATA_CALCULATED_SUMMARY') {
                        if (cell.value >= 0) cellClasses += ' cell-positive-background';
                        else if (cell.value < 0) cellClasses += ' cell-negative-background';
                    }
                    cell.computedClass = cellClasses.trim();
                });
            }
        });
        this.cashFlowData = updatedData;
    }

    // ────────────────────────────────────────────────────────────────────────────
    // 6) Helper: showToast
    // ────────────────────────────────────────────────────────────────────────────

    _showToast(title, message, variant) {
        this.dispatchEvent(
            new ShowToastEvent({
                title,
                message,
                variant,
                mode: 'dismissable'
            })
        );
    }
}