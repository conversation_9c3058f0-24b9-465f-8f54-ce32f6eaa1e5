/* eslint-disable */
import { LightningElement, api } from 'lwc';

export default class ContractValue extends LightningElement {
  @api projectData           = [];
  @api cashFlowData          = [];
  @api contractValueSeries   = [];

  get displayRows() {
    return this.contractValueSeries.map((entry, idx) => {
      const { iso, beg, payApp, ending } = entry;
      return {
        id:     `${iso}-${idx}`,
        date:   new Date(iso).toLocaleDateString(),
        beg:    `$${beg.toFixed(2)}`,
        payApp: `$${payApp.toFixed(2)}`,
        ending: `$${ending.toFixed(2)}`
      };
    });
  }
}