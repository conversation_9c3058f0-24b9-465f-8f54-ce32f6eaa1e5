/* eslint-disable */
import { LightningElement, api } from 'lwc';

export default class OutstandingLessOrigFees extends LightningElement {
  @api cashFlowData         = [];
  @api weekColumns          = [];
  @api opportunityData      = [];
  @api outstandingLessOrig  = [];

  get displayRows() {
    return this.outstandingLessOrig.map((entry, idx) => {
      return {
        id:    `${entry.iso}-${idx}`,
        date:  new Date(entry.iso).toLocaleDateString(),
        value: `$${entry.value.toFixed(2)}`
      };
    });
  }
}