// /* eslint-disable */
// import { LightningElement, api } from "lwc";

// export default class OutstandingLessOrigFees extends LightningElement {
//   /** data passed in */
//   @api transactions = [];
//   @api disbursements = [];
//   @api weekColumns = [];

//   /** grab projectId from first disbursement */
//   get projectId() {
//     return Array.isArray(this.disbursements) && this.disbursements.length
//       ? this.disbursements[0].Project__c
//       : null;
//   }

//   /**
//    * Given a JS Date, return the Date object corresponding to that week’s Friday.
//    * If the date is already Friday (getDay() === 5), return it (with hours zeroed).
//    * Otherwise, move forward to the next Friday.
//    */
//   getNextFriday(date) {
//     const d = new Date(date);
//     const dow = d.getDay(); // Sunday=0, Monday=1, …, Friday=5, Saturday=6
//     const offset = (5 - dow + 7) % 7;
//     d.setDate(d.getDate() + offset);
//     d.setHours(0, 0, 0, 0);
//     return d;
//   }

//   /** build the rows: one row per weekColumns entry with summed outstandingLessOrigFees */
//   get displayRows() {
//     // 1) Filter all transactions for this project
//     const txns =
//       Array.isArray(this.transactions) && this.projectId
//         ? this.transactions.filter((tx) => tx.Project__c === this.projectId)
//         : [];

//     // 2) Build a map of { "YYYY-MM-DD" → sumValue } in one step:
//     //    - For each transaction, compute outstandingLessOrigFees
//     //    - Determine the Friday bucket (as an ISO string)
//     //    - Accumulate sumValue by that ISO
//     const groupsByISO = txns.reduce((acc, tx) => {
//       // a) Compute loanDisb, origFees, payment, outstanding
//       const loanDisb = tx.Loan_Principal_to_date__c || 0;
//       const origFees =
//         (tx.Default_Fee_Application__c || 0) +
//         (tx.Doc_Stamp_Fees_Application__c || 0) +
//         (tx.Late_Fee_Application__c || 0) +
//         (tx.Legal_Fees_Application__c || 0);
//       const payment = tx.Amount__c || 0;
//       //const outstanding = loanDisb + origFees - payment;

//       // b) Compute outstandingLessOrigFees (as in your original code)
//       //const outstandingLessOrigFees = outstanding - payment;

//       // c) Determine which Friday this transaction belongs to
//       const createdDate = new Date(tx.CreatedDate);
//       if (isNaN(createdDate.getTime())) {
//         // skip if CreatedDate is invalid
//         return acc;
//       }
//       const weekFri = this.getNextFriday(createdDate);
//       if (isNaN(weekFri.getTime())) {
//         // skip if getNextFriday somehow returned invalid
//         return acc;
//       }

//       // d) Convert that Friday to an ISO string "YYYY-MM-DD"
//       const yyyy = weekFri.getFullYear();
//       const mm = String(weekFri.getMonth() + 1).padStart(2, "0");
//       const dd = String(weekFri.getDate()).padStart(2, "0");
//       const iso = `${yyyy}-${mm}-${dd}`;

//       // e) Accumulate into acc[iso].sumValue
//       if (!acc[iso]) {
//         acc[iso] = { loanDisbSum: 0, origFeesSum: 0, paymentSum: 0 };
//       }
//       acc[iso].loanDisbSum += loanDisb;
//       acc[iso].origFeesSum += origFees;
//       acc[iso].paymentSum += payment;
//       return acc;
//     }, {});

//     // 3) Now iterate exactly over weekColumns to produce one row per weekColumns entry
//     const rows = [];
//     let prevOutstanding = 0;
//     this.weekColumns.forEach((wkCol, idx) => {
//       // wkCol.date is expected to be an ISO string like "YYYY-MM-DD"
//       const isoDate = wkCol.date;
//       const { loanDisbSum = 0, origFeesSum = 0, paymentSum = 0 } = groupsByISO[isoDate] || {};

//        const currentOutstanding =
//         prevOutstanding + loanDisbSum + origFeesSum - paymentSum;
//       // now subtract this week's payment again
//       const outstandingLessOrigFees = currentOutstanding - paymentSum;

//       rows.push({
//         id: isoDate, // or isoDate + '-' + (idx+1) if you need guaranteed uniqueness
//         date: new Date(isoDate).toLocaleDateString(), // e.g. "6/6/2025"
//         week: idx + 1, // first element in weekColumns → week=1, next → week=2, etc.
//         value: `$ ${outstandingLessOrigFees.toFixed(2)}`,
//       });

//       prevOutstanding = currentOutstanding;
//     });

//     return rows;
//   }
// }


/* eslint-disable */
import { LightningElement, api } from "lwc";

export default class OutstandingLessOrigFees extends LightningElement {
  /** Incoming data */
  @api cashFlowData   = [];
  @api weekColumns    = [];
  @api opportunityData = [];

  /** loan‐fee rate fraction (e.g. 20% → 0.20) */
  get mfLoanRate() {
    const rec = Array.isArray(this.opportunityData) && this.opportunityData.length
      ? this.opportunityData[0]
      : this.opportunityData || {};
    const raw = rec.Closing_Cost__c;
    const pct = typeof raw === "number" ? raw : parseFloat(raw) || 0;
    return pct / 100;
  }

  /**
   * Reproduce your LoanDisbursement logic here to get
   * [{ iso, id, origFees, outstanding }, …]
   */
  computeLoanSeries() {
    const row = this.cashFlowData.find((r) => r.id === "MFLoanDisb");
    if (!row?.weeks) return [];

    // sort by date
    const weeks = [...row.weeks].sort(
      (a, b) => new Date(a.date) - new Date(b.date)
    );

    let prevOutstanding = 0;
    return weeks.map((wk) => {
      const loanDisb = wk.value || 0;
      const origFees = loanDisb / (1 - this.mfLoanRate) - loanDisb;
      const payment  = wk.payment || 0;
      const outstanding =
        prevOutstanding + loanDisb + origFees - payment;

      prevOutstanding = outstanding;
      return {
        iso:         wk.date,
        id:          wk.id,
        origFees,
        outstanding
      };
    });
  }

  /** one row per weekColumn, subtracting out origFees */
  get displayRows() {
    const series = this.computeLoanSeries();
    if (series.length === 0) {
      return [];
    }

    return this.weekColumns.map((wc, idx) => {
      const iso   = wc.date;
      const entry = series.find((e) => e.iso === iso) || {
        origFees: 0,
        outstanding: 0
      };
      const less = entry.outstanding - entry.origFees;

      return {
        id:    `${iso}-${idx}`,
        date:  new Date(iso).toLocaleDateString(),
        week:  idx + 1,
        value: `$ ${less.toFixed(2)}`
      };
    });
  }
}