// Trigger: only fires AFTER insert/update when CompletedAt__c is just set
trigger CashflowSessionTrigger on Cash_Flow_Session__c (after insert, after update) {
    List<Cash_Flow_Session__c> toProcess = new List<Cash_Flow_Session__c>();
    
    if (Trigger.isInsert) {
        for (Cash_Flow_Session__c sess : Trigger.new) {
            if (sess.CompletedAt__c != null) {
                toProcess.add(sess);
            }
        }
    }
    if (Trigger.isUpdate) {
        for (Integer i = 0; i < Trigger.new.size(); i++) {
            Cash_Flow_Session__c oldSess = Trigger.old[i];
            Cash_Flow_Session__c newSess = Trigger.new[i];
            if (oldSess.CompletedAt__c == null
                && newSess.CompletedAt__c != null) {
                toProcess.add(newSess);
            }
        }
    }
    
    if (!toProcess.isEmpty()) {
        System.debug('CashflowSessionService is calling');
        System.debug('toprocess '+toProcess);
        CashflowSessionService.processCompletedSessions(toProcess);
    }
}