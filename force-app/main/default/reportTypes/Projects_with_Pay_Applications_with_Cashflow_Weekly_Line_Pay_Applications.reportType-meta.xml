<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>Project__c</baseObject>
    <category>other</category>
    <deployed>true</deployed>
    <description>Projects with Pay Applications with Cashflow Weekly Line Pay Applications</description>
    <join>
        <join>
            <outerJoin>false</outerJoin>
            <relationship>Cashflow_Weekly_Line_Pay_Applications__r</relationship>
        </join>
        <outerJoin>false</outerJoin>
        <relationship>Pay_Application__r</relationship>
    </join>
    <label>Projects with Pay Applications with Cashflow Weekly Line Pay Applications</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Are_contacts_fully_executed__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Communication_Metric__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Does_borrower_meet_insurance_requirement__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>First_Day_of_the_Month__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Funds_Control_Metric__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Is_Project_Owner_in_Good_Standing__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Is_the_project_Bonded__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Loan_Status__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>MF_CRM__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>MF_Loan_Budget_Metric__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Monthly_Bank_Statement_Received__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Timeline_Compliance_Metric__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Quarterly_Financials_Recieved__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Request_for_Information_Metric__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Schedule_of_values_available_in_contract__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>State__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>System_Utilization_Metric__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Today_s_Date__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Union_Labor_on_Project__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Will_the_GC_sign_Funds_Directive__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>of_Days_This_Year__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>of_Paid_Disbursements__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Account_Name__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Accrued_Interest__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Additional_Project_Details__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Assigned_MF_Servicer__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Bank_Statement_Reviewed__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Calculate_Impact_Fees__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Change_Order_Amount__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>City__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Closing_Cost_Fees__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Closing_Costs_Flow_Helper__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Comment_Insurance_Requirement__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Comment_Location_of_GC__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Comment_Name_of_GC__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Comment_Payment_Terms__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Comments_Good_Standing__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Comments_Project_Bonded__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contract_Fully_Executed_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Contract_Start_Date_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Current_Loan_Balance__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Date_Loan_Paid_Off__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Date_to_Funded__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Default_Date__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Default_Fees__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Doc_Stamp_Fees__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Email_for_Servicing_Updates__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_Location_of_Project__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_Name_of_Project__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_contract_start_date__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_location_of_GC__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_location_of_Project_Owner__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_name_of_GC__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_name_of_Project_Owner__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_payment_terms__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_retainage__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_the_Length_of_the_Project__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_total_contract_s_amount__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Enter_type_of_Project_Owner__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Estimated_Completion_Date__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Expected_Billing_Total__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Follow_Up_Items__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Funds_Directive_sent_to_Owner_AP__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Last_Payment_Amount__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Last_Payment_Date__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Late_Fees__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Legal_Fees__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Loan_Maturity_Date__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Loan_Opportunity__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Loan_Principal__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>MF_Loan_Amount__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Materials_Actual__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Materials_Estimated__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Materials_Remaining__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Max_SRP_Payout__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Name_of_Email_Recipient__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Payroll_Actual__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Payroll_Estimated__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Payroll_Remaining__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Plaid_Accounts_Linked__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Postal_Zip_Code__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Auto__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Contract_Number__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Length_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Location_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Name_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Owner_Location_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Owner_Name_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Owner_Type_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Specific__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Start_Date__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Referral_Source_Account__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Referral_Source__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Remaining_Contract_Value__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Retainage_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Retainage__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Schedule_of_Values_in_Contract_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Scorecard_Comments__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Street__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Total_Contract_Amount_Comment__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Total_Tranche_s_Remaining__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Valley_Account_Balance__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Welcome_Call__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Aging_Days_Outstanding__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Closing_Costs__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Current_Pay_Off_Balance__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Money_Available_to_Draw_on_the_Loan__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Per_Diem_Interest__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Project_Number__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Total_Loan_Disbursements__c</field>
            <table>Project__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Date_Funded__c</field>
            <table>Project__c</table>
        </columns>
        <masterLabel>Projects</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pay_App_Paid_Unpaid__c</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Amount_applied_to_loan__c</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Current_Payment_Due__c</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pay_App_Date__c</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Planned_Loan_Payment_Date__c</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Projected_payment__c</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>of_Pay_App_to_Loan__c</field>
            <table>Project__c.Pay_Application__r</table>
        </columns>
        <masterLabel>Pay Applications</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Project__c.Pay_Application__r.Cashflow_Weekly_Line_Pay_Applications__r</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Project__c.Pay_Application__r.Cashflow_Weekly_Line_Pay_Applications__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Project__c.Pay_Application__r.Cashflow_Weekly_Line_Pay_Applications__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Project__c.Pay_Application__r.Cashflow_Weekly_Line_Pay_Applications__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Project__c.Pay_Application__r.Cashflow_Weekly_Line_Pay_Applications__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Project__c.Pay_Application__r.Cashflow_Weekly_Line_Pay_Applications__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>Project__c.Pay_Application__r.Cashflow_Weekly_Line_Pay_Applications__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Cashflow_Line_Item__c</field>
            <table>Project__c.Pay_Application__r.Cashflow_Weekly_Line_Pay_Applications__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Amount__c</field>
            <table>Project__c.Pay_Application__r.Cashflow_Weekly_Line_Pay_Applications__r</table>
        </columns>
        <masterLabel>Cashflow Weekly Line Pay Applications</masterLabel>
    </sections>
</ReportType>
